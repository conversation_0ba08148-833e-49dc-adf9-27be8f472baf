{"actions": [], "allow_rename": 1, "autoname": "format:GP-{######}", "creation": "2025-06-16 15:53:23.193682", "doctype": "DocType", "engine": "InnoDB", "field_order": ["column_break_axgo", "column_break_fggy", "date", "section_break_iczo", "html_gp", "section_break_eqeh", "transfer_from", "column_break_aexw", "transfer_to", "column_break_hphb", "vehicle_no", "section_break_arvs", "table_otpe", "section_break_rlob", "column_break_mibm", "remarks", "column_break_gagl", "yours_faithfully"], "fields": [{"fieldname": "column_break_axgo", "fieldtype": "Column Break"}, {"fieldname": "column_break_fggy", "fieldtype": "Column Break"}, {"fieldname": "date", "fieldtype": "Date", "in_list_view": 1, "label": "Date"}, {"fieldname": "section_break_iczo", "fieldtype": "Section Break"}, {"fieldname": "html_gp", "fieldtype": "HTML", "options": "<h3 style=\"text-align: center;\">To Whom So Ever It May Concern</h3>", "read_only": 1}, {"fieldname": "section_break_eqeh", "fieldtype": "Section Break"}, {"fieldname": "transfer_from", "fieldtype": "Data", "in_list_view": 1, "label": "Transfer From"}, {"fieldname": "column_break_aexw", "fieldtype": "Column Break"}, {"fieldname": "transfer_to", "fieldtype": "Data", "in_list_view": 1, "label": "Transfer To"}, {"fieldname": "column_break_hphb", "fieldtype": "Column Break"}, {"fieldname": "vehicle_no", "fieldtype": "Data", "in_list_view": 1, "label": "Vehicle No"}, {"fieldname": "section_break_arvs", "fieldtype": "Section Break"}, {"fieldname": "table_otpe", "fieldtype": "Table", "options": "Gate Pass Items"}, {"fieldname": "section_break_rlob", "fieldtype": "Section Break"}, {"fieldname": "column_break_mibm", "fieldtype": "Column Break"}, {"fieldname": "remarks", "fieldtype": "Small Text", "label": "Remarks"}, {"fieldname": "column_break_gagl", "fieldtype": "Column Break"}, {"fieldname": "yours_faithfully", "fieldtype": "Signature", "label": "Yours Faithfully"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-06-16 17:42:24.915066", "modified_by": "Administrator", "module": "Gate Pass", "name": "Gate Pass", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}