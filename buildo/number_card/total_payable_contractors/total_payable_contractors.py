# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.utils import flt


@frappe.whitelist()
def get_total_contractor_balance():
    """Calculate total running balance for all contractors"""
    try:
        # Get all contractors
        contractors = frappe.get_all("Contractors", fields=["name"])
        
        total_balance = 0
        
        for contractor in contractors:
            contractor_balance = get_contractor_running_balance(contractor.name)
            total_balance += contractor_balance
        
        return flt(total_balance)
    
    except Exception as e:
        frappe.log_error(f"Error calculating total contractor balance: {str(e)}")
        return 0


def get_contractor_running_balance(contractor_name):
    """Calculate running balance for a specific contractor"""
    try:
        # Get completed schedule amounts (debits)
        schedule_amount = frappe.db.sql("""
            SELECT COALESCE(SUM(cst.amount), 0) as total_amount
            FROM `tabC Schedule` cs
            INNER JOIN `tabC Schedule Table` cst ON cs.name = cst.parent
            WHERE cs.contractor = %s 
            AND cst.status = 'Completed'
            AND cst.c_date IS NOT NULL
            AND cs.docstatus != 2
        """, (contractor_name,))[0][0] or 0
        
        # Get payment amounts (credits)
        payment_amount = frappe.db.sql("""
            SELECT COALESCE(SUM(amount), 0) as total_amount
            FROM `tabContractor Payments`
            WHERE contractor = %s
            AND docstatus != 2
        """, (contractor_name,))[0][0] or 0
        
        # Running balance = Schedule amounts - Payment amounts
        running_balance = flt(schedule_amount) - flt(payment_amount)
        
        return running_balance
    
    except Exception as e:
        frappe.log_error(f"Error calculating contractor balance for {contractor_name}: {str(e)}")
        return 0
