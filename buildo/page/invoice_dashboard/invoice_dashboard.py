# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.utils import formatdate, format_value


@frappe.whitelist()
def get_recent_invoices():
    """Get recent client and supplier invoices for dashboard"""
    
    try:
        # Get recent client invoices
        client_invoices = frappe.get_all(
            "Client Invoice",
            fields=["name", "date", "subject", "net_payable", "balance_amount"],
            order_by="creation desc",
            limit=5
        )
        
        # Format client invoice data
        for invoice in client_invoices:
            invoice.date = formatdate(invoice.date) if invoice.date else ""
            invoice.subject = (invoice.subject or "")[:30] + "..." if len(invoice.subject or "") > 30 else (invoice.subject or "")
            invoice.net_payable = format_value(invoice.net_payable or 0, {"fieldtype": "Currency"})
            invoice.balance_amount = format_value(invoice.balance_amount or 0, {"fieldtype": "Currency"})
        
        # Get recent supplier invoices
        supplier_invoices = frappe.get_all(
            "Supplier Invoices",
            fields=["name", "date", "supplier_invoice_no", "grand_total", "balance_amount"],
            order_by="creation desc",
            limit=5
        )
        
        # Format supplier invoice data
        for invoice in supplier_invoices:
            invoice.date = formatdate(invoice.date) if invoice.date else ""
            invoice.supplier_invoice_no = (invoice.supplier_invoice_no or "")[:20] + "..." if len(invoice.supplier_invoice_no or "") > 20 else (invoice.supplier_invoice_no or "")
            invoice.grand_total = format_value(invoice.grand_total or 0, {"fieldtype": "Currency"})
            invoice.balance_amount = format_value(invoice.balance_amount or 0, {"fieldtype": "Currency"})
        
        return {
            "client_invoices": client_invoices,
            "supplier_invoices": supplier_invoices
        }
        
    except Exception as e:
        frappe.log_error(f"Error getting recent invoices: {str(e)}")
        return {
            "client_invoices": [],
            "supplier_invoices": []
        }
