frappe.pages['invoice-dashboard'].on_page_load = function(wrapper) {
    var page = frappe.ui.make_app_page({
        parent: wrapper,
        title: 'Recent Invoices',
        single_column: true
    });
    
    frappe.invoice_dashboard = new InvoiceDashboard(page);
}

class InvoiceDashboard {
    constructor(page) {
        this.page = page;
        this.make();
    }
    
    make() {
        this.page.main.html(`
            <div class="invoice-dashboard">
                <div class="row">
                    <div class="col-md-12">
                        <h4 style="color: #C7CEEA; margin-bottom: 20px;">📋 Recent Invoices</h4>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div id="client-invoices-container"></div>
                    </div>
                    <div class="col-md-6">
                        <div id="supplier-invoices-container"></div>
                    </div>
                </div>
            </div>
        `);
        
        this.load_data();
    }
    
    load_data() {
        frappe.call({
            method: 'buildocustom.buildo.page.invoice_dashboard.invoice_dashboard.get_recent_invoices',
            callback: (r) => {
                if (r.message) {
                    this.render_client_invoices(r.message.client_invoices);
                    this.render_supplier_invoices(r.message.supplier_invoices);
                }
            }
        });
    }
    
    render_client_invoices(invoices) {
        let html = `
            <div style="background: linear-gradient(135deg, #FFE5E5 0%, #FFF0F5 100%); padding: 15px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                <h6 style="color: #D63384; margin-bottom: 15px; font-weight: 600;">
                    📋 Recent Client Invoices
                </h6>
                <div class="table-responsive">
                    <table class="table table-sm" style="background: white; border-radius: 6px; overflow: hidden;">
                        <thead style="background: linear-gradient(135deg, #F8F9FA 0%, #E9ECEF 100%);">
                            <tr>
                                <th style="font-size: 11px; font-weight: 600; color: #495057;">Date</th>
                                <th style="font-size: 11px; font-weight: 600; color: #495057;">Subject</th>
                                <th style="font-size: 11px; font-weight: 600; color: #495057; text-align: right;">Net Payable</th>
                                <th style="font-size: 11px; font-weight: 600; color: #495057; text-align: right;">Balance</th>
                            </tr>
                        </thead>
                        <tbody>
        `;
        
        if (invoices && invoices.length > 0) {
            invoices.forEach(invoice => {
                html += `
                    <tr>
                        <td style="font-size: 11px; color: #495057;">${invoice.date}</td>
                        <td style="font-size: 11px; color: #495057;" title="${invoice.subject}">${invoice.subject}</td>
                        <td style="font-size: 11px; color: #495057; text-align: right; font-weight: 500;">${invoice.net_payable}</td>
                        <td style="font-size: 11px; font-weight: 600; color: #DC3545; text-align: right;">${invoice.balance_amount}</td>
                    </tr>
                `;
            });
        } else {
            html += `
                <tr>
                    <td colspan="4" style="padding: 30px; text-align: center; color: #6C757D; font-style: italic;">No recent invoices found</td>
                </tr>
            `;
        }
        
        html += `
                        </tbody>
                    </table>
                </div>
                <div style="text-align: center; margin-top: 12px;">
                    <a href="/app/client-invoice" class="btn btn-sm btn-outline-primary">View All →</a>
                </div>
            </div>
        `;
        
        $('#client-invoices-container').html(html);
    }
    
    render_supplier_invoices(invoices) {
        let html = `
            <div style="background: linear-gradient(135deg, #E5F3E5 0%, #F0FFF0 100%); padding: 15px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                <h6 style="color: #198754; margin-bottom: 15px; font-weight: 600;">
                    📋 Recent Supplier Invoices
                </h6>
                <div class="table-responsive">
                    <table class="table table-sm" style="background: white; border-radius: 6px; overflow: hidden;">
                        <thead style="background: linear-gradient(135deg, #F8F9FA 0%, #E9ECEF 100%);">
                            <tr>
                                <th style="font-size: 11px; font-weight: 600; color: #495057;">Date</th>
                                <th style="font-size: 11px; font-weight: 600; color: #495057;">Invoice No</th>
                                <th style="font-size: 11px; font-weight: 600; color: #495057; text-align: right;">Grand Total</th>
                                <th style="font-size: 11px; font-weight: 600; color: #495057; text-align: right;">Balance</th>
                            </tr>
                        </thead>
                        <tbody>
        `;
        
        if (invoices && invoices.length > 0) {
            invoices.forEach(invoice => {
                html += `
                    <tr>
                        <td style="font-size: 11px; color: #495057;">${invoice.date}</td>
                        <td style="font-size: 11px; color: #495057;" title="${invoice.supplier_invoice_no}">${invoice.supplier_invoice_no}</td>
                        <td style="font-size: 11px; color: #495057; text-align: right; font-weight: 500;">${invoice.grand_total}</td>
                        <td style="font-size: 11px; font-weight: 600; color: #DC3545; text-align: right;">${invoice.balance_amount}</td>
                    </tr>
                `;
            });
        } else {
            html += `
                <tr>
                    <td colspan="4" style="padding: 30px; text-align: center; color: #6C757D; font-style: italic;">No recent invoices found</td>
                </tr>
            `;
        }
        
        html += `
                        </tbody>
                    </table>
                </div>
                <div style="text-align: center; margin-top: 12px;">
                    <a href="/app/supplier-invoices" class="btn btn-sm btn-outline-success">View All →</a>
                </div>
            </div>
        `;
        
        $('#supplier-invoices-container').html(html);
    }
}
