frappe.dashboards.chart_sources["Invoice vs Payments"] = {
	method: "buildocustom.buildo.dashboard_chart_source.invoice_vs_payments.invoice_vs_payments.get_data",
	filters: [
		{
			fieldname: "project",
			label: __("Project"),
			fieldtype: "<PERSON>",
			options: "Projects"
		},
		{
			fieldname: "customer", 
			label: __("Customer"),
			fieldtype: "Link",
			options: "Customers"
		},
		{
			fieldname: "from_date",
			label: __("From Date"),
			fieldtype: "Date",
			default: frappe.datetime.add_months(frappe.datetime.get_today(), -12)
		},
		{
			fieldname: "to_date",
			label: __("To Date"),
			fieldtype: "Date",
			default: frappe.datetime.get_today()
		}
	]
};
