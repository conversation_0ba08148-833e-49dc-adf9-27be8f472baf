{"actions": [], "allow_rename": 1, "autoname": "format:MR-CEMENT-{######}", "creation": "2025-06-16 19:18:00.556638", "doctype": "DocType", "engine": "InnoDB", "field_order": ["date", "supplier", "vehicle_no", "column_break_ntuz", "project", "challan_no", "grade", "total_bags"], "fields": [{"fieldname": "date", "fieldtype": "Date", "in_list_view": 1, "label": "Date", "reqd": 1}, {"fieldname": "supplier", "fieldtype": "Link", "in_list_view": 1, "label": "Supplier", "options": "Suppliers", "reqd": 1}, {"fieldname": "vehicle_no", "fieldtype": "Data", "label": "Vehicle No"}, {"fieldname": "column_break_ntuz", "fieldtype": "Column Break"}, {"fieldname": "project", "fieldtype": "Link", "in_list_view": 1, "label": "Project", "options": "Projects", "reqd": 1}, {"fieldname": "challan_no", "fieldtype": "Data", "in_list_view": 1, "label": "Challan No", "reqd": 1}, {"default": "OPC 53", "fieldname": "grade", "fieldtype": "Select", "label": "Grade", "options": "\nOPC 53\nOPC 43\nPPC"}, {"fieldname": "total_bags", "fieldtype": "Float", "label": "Total Bags"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-06-20 11:07:32.006094", "modified_by": "Administrator", "module": "Material Registers", "name": "Cement Register", "naming_rule": "Expression (old style)", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}