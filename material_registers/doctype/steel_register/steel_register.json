{"actions": [], "allow_rename": 1, "autoname": "format:MR-STEEL-{######}", "creation": "2025-06-16 18:42:31.735426", "doctype": "DocType", "engine": "InnoDB", "field_order": ["date", "project", "column_break_vddg", "supplier", "challan_no", "calculate_bundlewise", "no_of_bars_section", "8_mm", "12_mm", "20_mm", "32_mm", "column_break_kjef", "10_mm", "16_mm", "25_mm", "total_mt", "no_of_bundles_section", "8_mm_b", "12_mm_b", "20_mm_b", "32_mm_b", "column_break_swjf", "10_mm_b", "16_mm_b", "25_mm_b", "total_bundle", "section_break_mzbf", "remarks"], "fields": [{"fieldname": "date", "fieldtype": "Date", "in_list_view": 1, "label": "Date", "reqd": 1}, {"fieldname": "project", "fieldtype": "Link", "in_list_view": 1, "label": "Project", "options": "Projects", "reqd": 1}, {"fieldname": "column_break_vddg", "fieldtype": "Column Break"}, {"fieldname": "supplier", "fieldtype": "Link", "in_list_view": 1, "label": "Supplier", "options": "Suppliers", "reqd": 1}, {"fieldname": "challan_no", "fieldtype": "Data", "in_list_view": 1, "label": "Challan No", "reqd": 1}, {"default": "0", "fieldname": "calculate_bundlewise", "fieldtype": "Check", "label": "calculate bundlewise"}, {"depends_on": "eval:doc.calculate_bundlewise == 0", "fieldname": "no_of_bars_section", "fieldtype": "Section Break", "label": "No of Bars"}, {"fieldname": "8_mm", "fieldtype": "Float", "label": "8 mm", "precision": "2"}, {"fieldname": "12_mm", "fieldtype": "Float", "label": "12 mm", "precision": "2"}, {"fieldname": "20_mm", "fieldtype": "Float", "label": "20 mm", "precision": "2"}, {"fieldname": "32_mm", "fieldtype": "Float", "label": "32 mm", "precision": "2"}, {"fieldname": "column_break_kjef", "fieldtype": "Column Break"}, {"fieldname": "10_mm", "fieldtype": "Float", "label": "10 mm", "precision": "2"}, {"fieldname": "16_mm", "fieldtype": "Float", "label": "16 mm", "precision": "2"}, {"fieldname": "25_mm", "fieldtype": "Float", "label": "25 mm", "precision": "2"}, {"fieldname": "total_mt", "fieldtype": "Float", "in_list_view": 1, "label": "Total MT", "precision": "2"}, {"depends_on": "eval:doc.calculate_bundlewise == 1", "fieldname": "no_of_bundles_section", "fieldtype": "Section Break", "label": "No Of Bundles"}, {"fieldname": "8_mm_b", "fieldtype": "Float", "label": "8 mm", "precision": "2"}, {"fieldname": "12_mm_b", "fieldtype": "Float", "label": "12 mm", "precision": "2"}, {"fieldname": "20_mm_b", "fieldtype": "Float", "label": "20 mm", "precision": "2"}, {"fieldname": "32_mm_b", "fieldtype": "Float", "label": "32 mm", "precision": "2"}, {"fieldname": "column_break_swjf", "fieldtype": "Column Break"}, {"fieldname": "10_mm_b", "fieldtype": "Float", "label": "10 mm", "precision": "2"}, {"fieldname": "16_mm_b", "fieldtype": "Float", "label": "16 mm", "precision": "2"}, {"fieldname": "25_mm_b", "fieldtype": "Float", "label": "25 mm", "precision": "2"}, {"fieldname": "total_bundle", "fieldtype": "Float", "in_list_view": 1, "label": "Total Bundle", "precision": "2"}, {"fieldname": "section_break_mzbf", "fieldtype": "Section Break"}, {"fieldname": "remarks", "fieldtype": "Small Text", "label": "Remarks"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-06-20 11:06:42.372777", "modified_by": "Administrator", "module": "Material Registers", "name": "Steel Register", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}