// Copyright (c) 2025, <PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.ui.form.on("rmc", {
    refresh: function(frm) {
        frm.events.calculate_total_qty(frm);
    },

    // Event handler for when the child table is rendered
    table_gaue_on_form_rendered: function(frm) {
        frm.events.calculate_total_qty(frm);
    },

    calculate_total_qty: function(frm) {
        let total_qty = 0;
        frm.doc.table_gaue.forEach(function(row) {
            total_qty += row.qty || 0;
        });
        frm.set_value('total_qty', total_qty);
    }
});

frappe.ui.form.on("rmc table", {
    qty: function(frm, cdt, cdn) {
        frm.events.calculate_total_qty(frm);
    }
});
