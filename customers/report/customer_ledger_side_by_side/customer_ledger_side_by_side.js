// Copyright (c) 2025, <PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.query_reports["Customer Ledger Side by Side"] = {
	"filters": [
		{
			"fieldname": "project",
			"label": __("Project"),
			"fieldtype": "Link",
			"options": "Projects",
			"width": "100px"
		},
		{
			"fieldname": "customer",
			"label": __("Customer"),
			"fieldtype": "Link",
			"options": "Customers",
			"width": "100px"
		},
		{
			"fieldname": "from_date",
			"label": __("From Date"),
			"fieldtype": "Date",
			"width": "100px"
		},
		{
			"fieldname": "to_date",
			"label": __("To Date"),
			"fieldtype": "Date",
			"width": "100px"
		}
	],

	"onload": function(report) {
		// Add custom styling for the HTML report
		this.add_custom_styles();

		// Add refresh button
		report.page.add_inner_button(__("Refresh"), function() {
			report.refresh();
		});

		// Add custom export button
		report.page.add_inner_button(__("Export to PDF"), function() {
			frappe.customer_ledger_side_by_side.export_to_pdf(report);
		});

		// Add custom print button
		report.page.add_inner_button(__("Print"), function() {
			frappe.customer_ledger_side_by_side.print_report(report);
		});
	},

	"add_custom_styles": function() {
		// Add custom CSS for the HTML-based side-by-side report
		if (!document.getElementById('customer-ledger-html-styles')) {
			const style = document.createElement('style');
			style.id = 'customer-ledger-html-styles';
			style.textContent = `
				/* Custom styles for HTML-based Customer Ledger Side by Side report */
				.report-wrapper .datatable .dt-row .dt-cell {
					padding: 0 !important;
					border: none !important;
					vertical-align: top !important;
					height: auto !important;
					min-height: 600px !important;
				}

				.report-wrapper .datatable .dt-header {
					display: none !important;
				}

				.report-wrapper .datatable .dt-row {
					height: auto !important;
					min-height: 600px !important;
				}

				.report-wrapper .datatable .dt-cell .dt-cell__content {
					height: auto !important;
					min-height: 600px !important;
					overflow: visible !important;
				}

				/* Responsive design for smaller screens */
				@media (max-width: 1200px) {
					.report-wrapper .datatable .dt-cell > div > div:nth-child(2) {
						flex-direction: column !important;
					}

					.report-wrapper .datatable .dt-cell > div > div:nth-child(2) > div {
						flex: none !important;
						width: 100% !important;
						margin-bottom: 20px;
					}
				}

				/* Print styles */
				@media print {
					.report-wrapper .datatable .dt-cell > div {
						page-break-inside: avoid;
					}
				}

				/* Hover effects for table rows */
				.report-wrapper table tbody tr:hover {
					background-color: #f0f8ff !important;
					transition: background-color 0.2s ease;
				}

				/* Scrollbar styling for tables */
				.report-wrapper div[style*="overflow-x: auto"]::-webkit-scrollbar {
					height: 6px;
				}

				.report-wrapper div[style*="overflow-x: auto"]::-webkit-scrollbar-track {
					background: #f1f1f1;
					border-radius: 3px;
				}

				.report-wrapper div[style*="overflow-x: auto"]::-webkit-scrollbar-thumb {
					background: #c1c1c1;
					border-radius: 3px;
				}

				.report-wrapper div[style*="overflow-x: auto"]::-webkit-scrollbar-thumb:hover {
					background: #a8a8a8;
				}

				/* Prevent datatable CSS errors */
				.report-wrapper .datatable {
					position: relative !important;
				}

				.report-wrapper .datatable .dt-scrollable {
					overflow: visible !important;
				}

				/* Ensure style elements exist */
				.report-wrapper .datatable .dt-style {
					display: none !important;
				}

				/* Enable horizontal scrolling for the report content */
				.report-wrapper .datatable .dt-scrollable {
					overflow-x: auto !important;
					overflow-y: visible !important;
				}

				.report-wrapper .datatable .dt-cell .dt-cell__content {
					overflow: visible !important;
					white-space: nowrap !important;
				}

				/* Ensure the main container allows horizontal scroll */
				.report-wrapper {
					overflow-x: auto !important;
				}
			`;
			document.head.appendChild(style);
		}
	},

	"formatter": function(value, row, column, data, default_formatter) {
		// Handle HTML content - return as-is for proper rendering
		if (column.fieldname === "html_content" && value) {
			return value;
		}

		return default_formatter(value, row, column, data);
	}
};

// Custom export and print functions
frappe.customer_ledger_side_by_side = {
	export_to_pdf: function(report) {
		// Get the HTML content from the report data
		if (report.data && report.data.length > 0 && report.data[0].html_content) {
			const html_content = report.data[0].html_content;

			// Create a new window with the HTML content for PDF export
			const printWindow = window.open('', '_blank');
			printWindow.document.write(`
				<!DOCTYPE html>
				<html>
				<head>
					<title>Customer Ledger Side by Side Report</title>
					<style>
						body {
							font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
							margin: 0;
							padding: 20px;
							font-size: 12px;
						}
						@media print {
							body { margin: 0; }
							.no-print { display: none; }
						}
						table { page-break-inside: avoid; }
						.summary-cards { margin-bottom: 20px; }
					</style>
				</head>
				<body>
					<h1>Customer Ledger Side by Side Report</h1>
					${html_content}
					<script>
						window.onload = function() {
							window.print();
							setTimeout(function() { window.close(); }, 1000);
						}
					</script>
				</body>
				</html>
			`);
			printWindow.document.close();
		} else {
			frappe.msgprint(__("No data to export"));
		}
	},

	print_report: function(report) {
		// Get the HTML content from the report data
		if (report.data && report.data.length > 0 && report.data[0].html_content) {
			const html_content = report.data[0].html_content;

			// Create a new window with the HTML content for printing
			const printWindow = window.open('', '_blank');
			printWindow.document.write(`
				<!DOCTYPE html>
				<html>
				<head>
					<title>Customer Ledger Side by Side Report</title>
					<style>
						body {
							font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
							margin: 0;
							padding: 20px;
							font-size: 12px;
						}
						@media print {
							body { margin: 0; padding: 10px; }
							.no-print { display: none; }
							table { page-break-inside: avoid; }
						}
						.summary-cards { margin-bottom: 20px; }
						table { width: 100%; border-collapse: collapse; }
						th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
						th { background-color: #f2f2f2; }
					</style>
				</head>
				<body>
					<h1>Customer Ledger Side by Side Report</h1>
					${html_content}
					<script>
						window.onload = function() {
							window.print();
						}
					</script>
				</body>
				</html>
			`);
			printWindow.document.close();
		} else {
			frappe.msgprint(__("No data to print"));
		}
	}
};

// Extend the original report configuration
Object.assign(frappe.query_reports["Customer Ledger Side by Side"], {
	"get_datatable_options": function(options) {
		// Customize datatable options for HTML content
		return Object.assign(options, {
			checkboxColumn: false,
			inlineFilters: false,
			layout: "fixed",  // Use fixed layout to prevent CSS issues
			noDataMessage: __("No data found for the selected filters"),
			cellHeight: 600,  // Set a large height to accommodate the HTML content
			dynamicRowHeight: false,  // Disable dynamic height to prevent CSS issues
			serialNoColumn: false,
			headerHeight: 0,
			showTotalRow: false,
			resizeColumn: false,  // Disable column resizing
			sortable: false  // Disable sorting to prevent layout issues
		});
	}
});

// Helper function to format currency
function format_currency(value) {
	if (!value) return "";
	return frappe.format(value, {
		fieldtype: "Currency",
		precision: 2
	});
}
