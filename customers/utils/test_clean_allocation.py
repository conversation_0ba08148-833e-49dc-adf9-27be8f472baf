# Test script for clean payment allocation implementation
import frappe
from frappe.utils import now_datetime, flt

def test_clean_allocation():
    """Test the clean payment allocation implementation"""
    try:
        print("Testing Clean Payment Allocation Implementation")
        print("=" * 50)
        
        # Use existing data
        customer = "Hiren"
        project = "dsaf"
        
        print(f"Customer: {customer}")
        print(f"Project: {project}")
        
        # Check outstanding invoices
        invoices = frappe.get_all(
            "Client Invoice",
            filters={
                "customer": customer,
                "project": project,
                "balance_amount": [">", 0]
            },
            fields=["name", "balance_amount", "net_payable"],
            order_by="date asc"
        )
        
        print(f"\nOutstanding Invoices: {len(invoices)}")
        total_outstanding = 0
        for inv in invoices:
            print(f"  - {inv.name}: Balance {inv.balance_amount}")
            total_outstanding += flt(inv.balance_amount)
        
        print(f"Total Outstanding: {total_outstanding}")
        
        if not invoices:
            print("No outstanding invoices found!")
            return False
        
        # Create a test payment
        payment_amount = 20000  # Test amount
        
        print(f"\nCreating payment of {payment_amount}...")
        
        payment = frappe.get_doc({
            "doctype": "Payment Received",
            "receipt_no": f"CLEAN-TEST-{now_datetime().strftime('%Y%m%d%H%M%S')}",
            "customer": customer,
            "project": project,
            "amount": payment_amount,
            "date": now_datetime().date(),
            "payment_mode": "Bank Transfer",
            "remarks": "Clean implementation test"
        })
        
        payment.insert()
        print(f"Payment created: {payment.name}")
        
        # Check if allocation happened automatically
        payment.reload()
        
        print(f"\nAfter Creation:")
        print(f"  - Unallocated Amount: {payment.unallocated_amount}")
        print(f"  - Allocation Details Count: {len(payment.allocation_details or [])}")
        
        # Check allocation records
        allocations = frappe.get_all(
            "Payment Allocation",
            filters={"payment_received": payment.name},
            fields=["client_invoice", "allocated_amount"]
        )
        
        print(f"  - Allocation Records: {len(allocations)}")
        total_allocated = 0
        for alloc in allocations:
            print(f"    * {alloc.client_invoice}: {alloc.allocated_amount}")
            total_allocated += flt(alloc.allocated_amount)
        
        print(f"  - Total Allocated: {total_allocated}")
        
        # Check allocation details child table
        if payment.allocation_details:
            print(f"  - Allocation Details:")
            for detail in payment.allocation_details:
                print(f"    * {detail.client_invoice}: {detail.allocated_amount} (Balance After: {detail.invoice_balance_after})")
        
        # Verify allocation worked
        expected_unallocated = payment_amount - total_allocated
        success = (
            len(allocations) > 0 and 
            abs(flt(payment.unallocated_amount) - expected_unallocated) < 0.01 and
            len(payment.allocation_details or []) > 0
        )
        
        print(f"\n" + "=" * 50)
        if success:
            print("✅ CLEAN ALLOCATION TEST PASSED!")
            print(f"   - Payment Amount: {payment_amount}")
            print(f"   - Total Allocated: {total_allocated}")
            print(f"   - Unallocated: {payment.unallocated_amount}")
            print(f"   - Allocation Records: {len(allocations)}")
            print(f"   - Allocation Details: {len(payment.allocation_details or [])}")
        else:
            print("❌ CLEAN ALLOCATION TEST FAILED!")
            print(f"   - Expected allocation but got:")
            print(f"   - Allocation Records: {len(allocations)}")
            print(f"   - Allocation Details: {len(payment.allocation_details or [])}")
            print(f"   - Unallocated Amount: {payment.unallocated_amount}")
        
        return success
        
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        frappe.log_error(f"Clean allocation test error: {str(e)}", "Clean Allocation Test Error")
        return False

def test_payment_modification():
    """Test payment modification and reallocation"""
    try:
        print("\nTesting Payment Modification...")
        
        # Get the last created payment
        payments = frappe.get_all(
            "Payment Received",
            filters={"receipt_no": ["like", "CLEAN-TEST-%"]},
            order_by="creation desc",
            limit=1
        )
        
        if not payments:
            print("No test payment found for modification test")
            return False
        
        payment = frappe.get_doc("Payment Received", payments[0].name)
        original_amount = payment.amount
        original_unallocated = payment.unallocated_amount
        
        print(f"Modifying payment {payment.name}")
        print(f"Original Amount: {original_amount}")
        print(f"Original Unallocated: {original_unallocated}")
        
        # Modify the amount
        new_amount = original_amount + 5000
        payment.amount = new_amount
        payment.save()
        
        # Reload and check
        payment.reload()
        
        print(f"New Amount: {payment.amount}")
        print(f"New Unallocated: {payment.unallocated_amount}")
        
        # Check if reallocation happened
        allocations = frappe.get_all(
            "Payment Allocation",
            filters={"payment_received": payment.name},
            fields=["client_invoice", "allocated_amount"]
        )
        
        total_allocated = sum(flt(alloc.allocated_amount) for alloc in allocations)
        expected_unallocated = new_amount - total_allocated
        
        success = abs(flt(payment.unallocated_amount) - expected_unallocated) < 0.01
        
        if success:
            print("✅ PAYMENT MODIFICATION TEST PASSED!")
        else:
            print("❌ PAYMENT MODIFICATION TEST FAILED!")
        
        return success
        
    except Exception as e:
        print(f"❌ MODIFICATION ERROR: {str(e)}")
        return False

if __name__ == "__main__":
    # Run tests
    test1_result = test_clean_allocation()
    test2_result = test_payment_modification()
    
    print(f"\n" + "=" * 50)
    print("FINAL RESULTS:")
    print(f"Clean Allocation Test: {'PASSED' if test1_result else 'FAILED'}")
    print(f"Payment Modification Test: {'PASSED' if test2_result else 'FAILED'}")
    
    if test1_result and test2_result:
        print("🎉 ALL TESTS PASSED! Clean implementation is working!")
    else:
        print("⚠️  Some tests failed. Check the implementation.")
