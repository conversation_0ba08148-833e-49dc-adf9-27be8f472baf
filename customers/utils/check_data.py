# Check existing data
import frappe
from frappe.utils import now_datetime, flt

def check_existing_data():
    """Check existing customers, projects, and invoices"""
    try:
        print("Checking existing data...")
        
        # Check customers
        customers = frappe.get_all("Customers", fields=["name"], limit=5)
        print(f"Customers found: {len(customers)}")
        for customer in customers:
            print(f"  - {customer.name}")
        
        # Check projects
        projects = frappe.get_all("Projects", fields=["name", "customer"], limit=5)
        print(f"Projects found: {len(projects)}")
        for project in projects:
            print(f"  - {project.name} (Customer: {project.customer})")
        
        # Check invoices
        invoices = frappe.get_all(
            "Client Invoice", 
            fields=["name", "customer", "project", "balance_amount", "status"],
            limit=10
        )
        print(f"Invoices found: {len(invoices)}")
        for invoice in invoices:
            print(f"  - {invoice.name}: {invoice.customer}/{invoice.project}, Balance: {invoice.balance_amount}, Status: {invoice.status}")
        
        # Find invoices with balance > 0
        outstanding_invoices = frappe.get_all(
            "Client Invoice",
            filters={"balance_amount": [">", 0]},
            fields=["name", "customer", "project", "balance_amount"],
            limit=5
        )
        print(f"Outstanding invoices: {len(outstanding_invoices)}")
        for invoice in outstanding_invoices:
            print(f"  - {invoice.name}: {invoice.customer}/{invoice.project}, Balance: {invoice.balance_amount}")
        
        return customers, projects, invoices, outstanding_invoices
        
    except Exception as e:
        print(f"Error checking data: {str(e)}")
        return [], [], [], []

def create_test_data():
    """Create test data if needed"""
    try:
        print("\nCreating test data...")
        
        # Create test customer
        customer_name = "Test Customer Auto"
        if not frappe.db.exists("Customers", customer_name):
            customer = frappe.get_doc({
                "doctype": "Customers",
                "customer_name": customer_name,
                "gst_no": "TEST123456789",
                "pan_no": "TESTPAN123"
            })
            customer.insert(ignore_permissions=True)
            print(f"Created customer: {customer_name}")
        else:
            print(f"Customer already exists: {customer_name}")
        
        # Create test project
        project_name = "Test Project Auto"
        if not frappe.db.exists("Projects", project_name):
            project = frappe.get_doc({
                "doctype": "Projects",
                "project_name": project_name,
                "customer": customer_name
            })
            project.insert(ignore_permissions=True)
            print(f"Created project: {project_name}")
        else:
            print(f"Project already exists: {project_name}")
        
        # Create test invoices
        for i in range(3):
            invoice_no = f"TEST-AUTO-{i+1}-{now_datetime().strftime('%Y%m%d%H%M%S')}"
            
            invoice = frappe.get_doc({
                "doctype": "Client Invoice",
                "customer": customer_name,
                "project": project_name,
                "invoice_no": invoice_no,
                "date": now_datetime().date(),
                "net_payable": 25000 + (i * 5000),
                "tds_amount": 1000,
                "balance_amount": 24000 + (i * 5000),
                "status": "Not Paid",
                "subject": f"Test Invoice Auto {i+1}",
                "sub_total": 20000 + (i * 5000),
                "total": 25000 + (i * 5000)
            })
            invoice.insert(ignore_permissions=True)
            print(f"Created invoice: {invoice.name} with balance {invoice.balance_amount}")
        
        return customer_name, project_name
        
    except Exception as e:
        print(f"Error creating test data: {str(e)}")
        return None, None

if __name__ == "__main__":
    customers, projects, invoices, outstanding = check_existing_data()
    
    if len(outstanding) == 0:
        print("\nNo outstanding invoices found. Creating test data...")
        customer, project = create_test_data()
        if customer and project:
            print(f"\nTest data created successfully!")
            print(f"Customer: {customer}")
            print(f"Project: {project}")
            print("You can now test the payment allocation with this data.")
    else:
        print(f"\nFound {len(outstanding)} outstanding invoices. You can use existing data for testing.")
