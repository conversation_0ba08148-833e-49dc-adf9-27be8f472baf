# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.utils import flt


@frappe.whitelist()
def test_balance_update_flow():
	"""
	Test the complete balance update flow
	"""
	try:
		# Create test customer
		customer_name = "Test Balance Customer"
		if not frappe.db.exists("Customers", customer_name):
			customer = frappe.get_doc({
				"doctype": "Customers",
				"customer_name": customer_name,
				"gst_no": "TEST123456789",
				"pan_no": "TESTPAN123"
			})
			customer.insert(ignore_permissions=True)
		
		# Create test project
		project_name = "Test Balance Project"
		if not frappe.db.exists("Projects", project_name):
			project = frappe.get_doc({
				"doctype": "Projects",
				"project_name": project_name,
				"contract_type": "Built Up"
			})
			project.insert(ignore_permissions=True)
		
		# Create test invoice
		invoice_no = "TEST-BAL-001"
		existing_invoice = frappe.db.exists("Client Invoice", {"invoice_no": invoice_no})
		
		if existing_invoice:
			invoice = frappe.get_doc("Client Invoice", existing_invoice)
		else:
			invoice = frappe.get_doc({
				"doctype": "Client Invoice",
				"customer": customer_name,
				"project": project_name,
				"invoice_no": invoice_no,
				"date": frappe.utils.today(),
				"net_payable": 10000,
				"balance_amount": 10000,
				"tds_amount": 0
			})
			invoice.insert(ignore_permissions=True)
		
		# Record initial balance
		initial_balance = flt(invoice.balance_amount)
		
		# Create test payment
		payment_no = "TEST-BAL-PAY-001"
		existing_payment = frappe.db.exists("Payment Received", {"receipt_no": payment_no})
		
		if existing_payment:
			# Delete existing payment to start fresh
			frappe.delete_doc("Payment Received", existing_payment, ignore_permissions=True)
		
		payment = frappe.get_doc({
			"doctype": "Payment Received",
			"customer": customer_name,
			"project": project_name,
			"receipt_no": payment_no,
			"amount": 6000,  # Partial payment
			"date": frappe.utils.today(),
			"payment_mode": "Bank Transfer"
		})
		payment.insert(ignore_permissions=True)
		
		# Reload invoice to get updated balance
		invoice.reload()
		balance_after_payment = flt(invoice.balance_amount)
		
		# Check allocation records
		allocations = frappe.get_all(
			"Payment Allocation",
			filters={"payment_received": payment.name},
			fields=["allocated_amount"]
		)
		
		total_allocated = sum(flt(alloc.allocated_amount) for alloc in allocations)
		
		# Test payment deletion
		payment.delete()
		
		# Reload invoice to check balance restoration
		invoice.reload()
		balance_after_deletion = flt(invoice.balance_amount)
		
		# Cleanup
		frappe.db.sql("DELETE FROM `tabPayment Allocation` WHERE customer = %s", (customer_name,))
		frappe.db.sql("DELETE FROM `tabPayment Received` WHERE customer = %s", (customer_name,))
		frappe.db.sql("DELETE FROM `tabClient Invoice` WHERE customer = %s", (customer_name,))
		
		if frappe.db.exists("Projects", project_name):
			frappe.delete_doc("Projects", project_name, ignore_permissions=True)
		if frappe.db.exists("Customers", customer_name):
			frappe.delete_doc("Customers", customer_name, ignore_permissions=True)
		
		frappe.db.commit()
		
		# Return test results
		return {
			"success": True,
			"initial_balance": initial_balance,
			"balance_after_payment": balance_after_payment,
			"balance_after_deletion": balance_after_deletion,
			"total_allocated": total_allocated,
			"expected_balance_after_payment": initial_balance - total_allocated,
			"balance_update_working": balance_after_payment == (initial_balance - total_allocated),
			"balance_restoration_working": balance_after_deletion == initial_balance,
			"message": "Balance update test completed successfully!"
		}
		
	except Exception as e:
		frappe.log_error(f"Error in balance update test: {str(e)}", "Balance Update Test Error")
		return {
			"success": False,
			"error": str(e)
		}


@frappe.whitelist()
def check_invoice_balance_calculation(invoice_name):
	"""
	Check how balance is calculated for a specific invoice
	"""
	try:
		invoice = frappe.get_doc("Client Invoice", invoice_name)
		
		# Get total allocated amount
		total_allocated = frappe.db.sql("""
			SELECT COALESCE(SUM(allocated_amount), 0) as total_allocated
			FROM `tabPayment Allocation`
			WHERE client_invoice = %s
		""", (invoice_name,))[0][0]
		
		# Calculate expected balance
		base_balance = flt(invoice.net_payable) - flt(invoice.tds_amount)
		expected_balance = max(0, base_balance - flt(total_allocated))
		
		# Get allocation details
		allocations = frappe.get_all(
			"Payment Allocation",
			filters={"client_invoice": invoice_name},
			fields=["payment_received", "allocated_amount", "allocation_date"]
		)
		
		return {
			"invoice_name": invoice_name,
			"net_payable": invoice.net_payable,
			"tds_amount": invoice.tds_amount,
			"current_balance": invoice.balance_amount,
			"base_balance": base_balance,
			"total_allocated": total_allocated,
			"expected_balance": expected_balance,
			"balance_correct": flt(invoice.balance_amount) == expected_balance,
			"allocations": allocations
		}
		
	except Exception as e:
		return {"error": str(e)}


@frappe.whitelist()
def force_recalculate_all_balances():
	"""
	Force recalculate balance for all invoices (use with caution)
	"""
	try:
		invoices = frappe.get_all("Client Invoice", fields=["name"])
		updated_count = 0
		
		for invoice in invoices:
			try:
				from buildocustom.customers.utils.payment_allocation import recalculate_invoice_balance
				recalculate_invoice_balance(invoice.name)
				updated_count += 1
			except Exception as e:
				frappe.log_error(f"Error recalculating balance for {invoice.name}: {str(e)}")
				continue
		
		frappe.db.commit()
		
		return {
			"success": True,
			"message": f"Recalculated balance for {updated_count} invoices",
			"total_invoices": len(invoices),
			"updated_count": updated_count
		}
		
	except Exception as e:
		return {"error": str(e)}
