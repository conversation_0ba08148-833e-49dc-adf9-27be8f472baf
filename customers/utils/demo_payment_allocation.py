# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.utils import flt
from buildocustom.customers.utils.payment_allocation import allocate_payment_to_invoices


@frappe.whitelist()
def demo_payment_allocation():
	"""
	Demonstration function to show payment allocation in action
	Creates sample data and shows the allocation process
	"""
	try:
		# Create demo customer if not exists
		customer_name = "Demo Customer"
		if not frappe.db.exists("Customers", customer_name):
			customer = frappe.get_doc({
				"doctype": "Customers",
				"customer_name": customer_name,
				"gst_no": "DEMO123456789",
				"pan_no": "DEMOPAN123"
			})
			customer.insert(ignore_permissions=True)
		
		# Create demo project if not exists
		project_name = "Demo Project"
		if not frappe.db.exists("Projects", project_name):
			project = frappe.get_doc({
				"doctype": "Projects",
				"project_name": project_name,
				"contract_type": "Built Up"
			})
			project.insert(ignore_permissions=True)
		
		# Create demo invoices
		invoices = []
		for i in range(1, 4):
			invoice_no = f"DEMO-INV-{i:03d}"
			
			# Check if invoice already exists
			existing = frappe.db.exists("Client Invoice", {"invoice_no": invoice_no})
			if not existing:
				invoice = frappe.get_doc({
					"doctype": "Client Invoice",
					"customer": customer_name,
					"project": project_name,
					"invoice_no": invoice_no,
					"date": frappe.utils.add_days(frappe.utils.today(), -30 + (i * 5)),
					"net_payable": 1000 * i,
					"balance_amount": 1000 * i
				})
				invoice.insert(ignore_permissions=True)
				invoices.append(invoice)
			else:
				invoices.append(frappe.get_doc("Client Invoice", existing))
		
		# Create demo payment
		payment_no = "DEMO-PAY-001"
		existing_payment = frappe.db.exists("Payment Received", {"receipt_no": payment_no})
		
		if not existing_payment:
			payment = frappe.get_doc({
				"doctype": "Payment Received",
				"customer": customer_name,
				"project": project_name,
				"receipt_no": payment_no,
				"amount": 2500,  # This will partially pay multiple invoices
				"date": frappe.utils.today(),
				"payment_mode": "Bank Transfer"
			})
			payment.insert(ignore_permissions=True)
		else:
			payment = frappe.get_doc("Payment Received", existing_payment)
		
		# Show allocation result
		result = {
			"message": "Demo payment allocation completed successfully!",
			"customer": customer_name,
			"project": project_name,
			"payment": {
				"name": payment.name,
				"amount": payment.amount,
				"unallocated_amount": payment.unallocated_amount or 0
			},
			"invoices": []
		}
		
		# Get updated invoice balances
		for invoice in invoices:
			invoice.reload()
			result["invoices"].append({
				"name": invoice.name,
				"invoice_no": invoice.invoice_no,
				"net_payable": invoice.net_payable,
				"balance_amount": invoice.balance_amount
			})
		
		# Get allocation records
		allocations = frappe.get_all(
			"Payment Allocation",
			filters={"payment_received": payment.name},
			fields=["client_invoice", "allocated_amount", "allocation_date"]
		)
		
		result["allocations"] = allocations
		
		return result
		
	except Exception as e:
		frappe.log_error(f"Error in demo payment allocation: {str(e)}", "Demo Payment Allocation Error")
		return {"error": str(e)}


@frappe.whitelist()
def cleanup_demo_data():
	"""
	Clean up demo data created by demo_payment_allocation
	"""
	try:
		# Delete demo payment allocations
		frappe.db.sql("DELETE FROM `tabPayment Allocation` WHERE customer = 'Demo Customer'")
		
		# Delete demo payments
		frappe.db.sql("DELETE FROM `tabPayment Received` WHERE customer = 'Demo Customer'")
		
		# Delete demo invoices
		frappe.db.sql("DELETE FROM `tabClient Invoice` WHERE customer = 'Demo Customer'")
		
		# Delete demo project
		if frappe.db.exists("Projects", "Demo Project"):
			frappe.delete_doc("Projects", "Demo Project", ignore_permissions=True)
		
		# Delete demo customer
		if frappe.db.exists("Customers", "Demo Customer"):
			frappe.delete_doc("Customers", "Demo Customer", ignore_permissions=True)
		
		frappe.db.commit()
		
		return {"message": "Demo data cleaned up successfully!"}
		
	except Exception as e:
		frappe.log_error(f"Error cleaning up demo data: {str(e)}", "Demo Cleanup Error")
		return {"error": str(e)}


@frappe.whitelist()
def test_payment_deletion():
	"""
	Test payment deletion and balance restoration
	"""
	try:
		# First run demo to create data
		demo_result = demo_payment_allocation()
		if "error" in demo_result:
			return demo_result
		
		payment_name = demo_result["payment"]["name"]
		
		# Get invoice balances before deletion
		invoices_before = []
		for inv in demo_result["invoices"]:
			invoices_before.append({
				"name": inv["name"],
				"balance_before_deletion": inv["balance_amount"]
			})
		
		# Delete the payment
		payment_doc = frappe.get_doc("Payment Received", payment_name)
		payment_doc.delete()
		
		# Get invoice balances after deletion
		invoices_after = []
		for inv_before in invoices_before:
			invoice_doc = frappe.get_doc("Client Invoice", inv_before["name"])
			invoices_after.append({
				"name": inv_before["name"],
				"balance_before_deletion": inv_before["balance_before_deletion"],
				"balance_after_deletion": invoice_doc.balance_amount
			})
		
		# Check if allocation records are deleted
		remaining_allocations = frappe.get_all(
			"Payment Allocation",
			filters={"payment_received": payment_name}
		)
		
		result = {
			"message": "Payment deletion test completed successfully!",
			"payment_deleted": payment_name,
			"invoices": invoices_after,
			"remaining_allocations": len(remaining_allocations)
		}
		
		# Cleanup
		cleanup_demo_data()
		
		return result
		
	except Exception as e:
		frappe.log_error(f"Error in payment deletion test: {str(e)}", "Payment Deletion Test Error")
		return {"error": str(e)}
