{"actions": [], "allow_rename": 1, "creation": "2025-06-18 15:50:13.955291", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["hsn_code", "desc", "uom", "qty", "rate", "amount", "pre_bill_qty", "pre_bill_amt", "curr_bill_qty", "curr_bill_amt"], "fields": [{"fieldname": "hsn_code", "fieldtype": "Data", "in_list_view": 1, "label": "HSN Code"}, {"fieldname": "desc", "fieldtype": "Data", "in_list_view": 1, "label": "Desc"}, {"fieldname": "uom", "fieldtype": "Data", "in_list_view": 1, "label": "UOM"}, {"fieldname": "qty", "fieldtype": "Float", "in_list_view": 1, "label": "Qty"}, {"fieldname": "rate", "fieldtype": "Float", "in_list_view": 1, "label": "Rate"}, {"fieldname": "amount", "fieldtype": "Float", "in_list_view": 1, "label": "Amount"}, {"fieldname": "pre_bill_qty", "fieldtype": "Float", "in_list_view": 1, "label": "Pre <PERSON>"}, {"fieldname": "pre_bill_amt", "fieldtype": "Float", "in_list_view": 1, "label": "Pre <PERSON>"}, {"fieldname": "curr_bill_qty", "fieldtype": "Float", "in_list_view": 1, "label": "<PERSON><PERSON><PERSON>"}, {"fieldname": "curr_bill_amt", "fieldtype": "Float", "in_list_view": 1, "label": "<PERSON><PERSON><PERSON>"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2025-06-18 16:34:54.832117", "modified_by": "Administrator", "module": "Customers", "name": "Item Rate Invoice Items", "owner": "Administrator", "permissions": [], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}