// Copyright (c) 2025, <PERSON><PERSON> and contributors
// For license information, please see license.txt


frappe.ui.form.on("Client Invoice", {
	refresh(frm) {
		// Add "Fetch row items" button above Built Up Items table
		if (frm.doc.contract_type === "Built Up") {
			frm.add_custom_button(__("Fetch row items"), function() {
				show_invoice_selector(frm, "built_up_items");
			}, __("Built Up Items"));
		}

		// Add "Fetch row items" button above Item Rate Items table
		if (frm.doc.contract_type === "Item Rate") {
			frm.add_custom_button(__("Fetch row items"), function() {
				show_invoice_selector(frm, "item_rate_items");
			}, __("Item Rate Items"));
		}



		// Show balance status indicators
		if (frm.doc.balance_amount > 0) {
			frm.dashboard.add_indicator(__("Outstanding: {0}", [format_currency(frm.doc.balance_amount)]), "orange");
		} else if (frm.doc.net_payable > 0) {
			frm.dashboard.add_indicator(__("Fully Paid"), "green");
		}
	},

	validate(frm) {
		// Validate built up items percentages before saving
		if (frm.doc.contract_type === "Built Up" && frm.doc.built_up_items) {
			let validation_errors = [];
			frm.doc.built_up_items.forEach(function(row, index) {
				let sum_perc = safe_float(row.prev_perc) + safe_float(row.curr_perc);
				let total_perc = safe_float(row.total_perc);

				if (sum_perc > total_perc) {
					validation_errors.push(__("Row {0}: Sum of Previous Percentage ({1}%) and Current Percentage ({2}%) cannot exceed Total Percentage ({3}%)",
						[index + 1, safe_float(row.prev_perc), safe_float(row.curr_perc), total_perc]));
				}
			});

			if (validation_errors.length > 0) {
				frappe.msgprint({
					title: __("Validation Error"),
					message: validation_errors.join("<br>"),
					indicator: "red"
				});
				frappe.validated = false;
				return false;
			}
		}
	},

	before_save(frm) {
		// Ensure all calculations are up to date before saving
		try {
			if (frm.doc.contract_type === "Built Up" && frm.doc.built_up_items) {
				frm.doc.built_up_items.forEach(function(row) {
					// Ensure numeric values are properly formatted
					row.prev_perc = safe_float(row.prev_perc);
					row.curr_perc = safe_float(row.curr_perc);
					row.total_perc = safe_float(row.total_perc);
					row.pre_bill_amt = safe_float(row.pre_bill_amt);
					row.curr_bill_amt = safe_float(row.curr_bill_amt);
				});
			}

			if (frm.doc.contract_type === "Item Rate" && frm.doc.item_rate_items) {
				frm.doc.item_rate_items.forEach(function(row) {
					// Ensure numeric values are properly formatted
					row.qty = safe_float(row.qty);
					row.rate = safe_float(row.rate);
					row.amount = safe_float(row.amount);
					row.pre_bill_qty = safe_float(row.pre_bill_qty);
					row.pre_bill_amt = safe_float(row.pre_bill_amt);
					row.curr_bill_qty = safe_float(row.curr_bill_qty);
					row.curr_bill_amt = safe_float(row.curr_bill_amt);
				});
			}
		} catch (error) {
			console.error("Error in before_save:", error);
			frappe.msgprint({
				title: __("Error"),
				message: __("An error occurred while preparing data for save. Please check your entries and try again."),
				indicator: "red"
			});
			frappe.validated = false;
			return false;
		}
	},

	onload(frm) {
		// Trigger calculations on load if initial values exist
		if (frm.doc.built_up_items && frm.doc.built_up_items.length) {
			// No need to trigger calculate_all_rows if total_value is static
		}

		// Listen for realtime balance updates
		if (!frm.doc.__islocal) {
			frappe.realtime.on("invoice_balance_updated", function(data) {
				if (data.invoice_name === frm.doc.name) {
					frm.set_value('balance_amount', data.new_balance);
					frm.refresh_field('balance_amount');

					// Update dashboard indicators
					frm.dashboard.clear_indicators();
					if (data.new_balance > 0) {
						frm.dashboard.add_indicator(__("Outstanding: {0}", [format_currency(data.new_balance)]), "orange");
					} else if (frm.doc.net_payable > 0) {
						frm.dashboard.add_indicator(__("Fully Paid"), "green");
					}
				}
			});
		}

		// Trigger calculations on load if initial values exist for Item Rate Items
		if (frm.doc.contract_type === "Item Rate" && frm.doc.item_rate_items && frm.doc.item_rate_items.length) {
			frm.doc.item_rate_items.forEach(function(row) {
				calculate_item_rate_amounts(frm, row.doctype, row.name);
			});
		}
		calculate_sub_total(frm);
	},

	total_value: function(frm) {
		// Trigger recalculation for all built_up_items when total_value changes
		if (frm.doc.built_up_items && frm.doc.built_up_items.length) {
			frm.doc.built_up_items.forEach(function(row) {
				let total_value = parseFloat(frm.doc.total_value) || 0;

				if (total_value === 0) {
					row.pre_bill_amt = 0;
					row.curr_bill_amt = 0;
				} else {
					// Calculate pre_bill_amt
					if (row.prev_perc !== undefined && row.prev_perc !== null) {
						row.pre_bill_amt = parseFloat((parseFloat(row.prev_perc) * total_value) / 100, 2);
					} else {
						row.pre_bill_amt = 0;
					}
					
					// Calculate curr_bill_amt
					if (row.curr_perc !== undefined && row.curr_perc !== null) {
						row.curr_bill_amt = parseFloat((parseFloat(row.curr_perc) * total_value) / 100, 2);
					} else {
						row.curr_bill_amt = 0;
					}
				}
			});
			frm.refresh_field('built_up_items');
		}
		calculate_sub_total(frm);
	},

	// Event handlers for percentage fields to trigger calculations
	retention_: function(frm) {
		calculate_invoice_amounts(frm);
	},
	tds_: function(frm) {
		calculate_invoice_amounts(frm);
	},
	cgst_: function(frm) {
		calculate_invoice_amounts(frm);
	},
	sgst_: function(frm) {
		calculate_invoice_amounts(frm);
	},
	igst_: function(frm) {
		calculate_invoice_amounts(frm);
	},
	sub_total: function(frm) {
		calculate_invoice_amounts(frm);
	}
});

// Function to calculate all invoice amounts
function calculate_invoice_amounts(frm) {
	let sub_total = parseFloat(frm.doc.sub_total) || 0;
	let retention_ = parseFloat(frm.doc.retention_) || 0;
	let tds_ = parseFloat(frm.doc.tds_) || 0;
	let cgst_ = parseFloat(frm.doc.cgst_) || 0;
	let sgst_ = parseFloat(frm.doc.sgst_) || 0;
	let igst_ = parseFloat(frm.doc.igst_) || 0;

	let retention_amount = (retention_ * sub_total) / 100;
	let total = sub_total - retention_amount;
	let cgst_amount = (cgst_ * total) / 100;
	let sgst_amount = (sgst_ * total) / 100;
	let igst_amount = (igst_ * total) / 100;
	let net_payable = total + cgst_amount + sgst_amount + igst_amount;
	let tds_amount = (tds_ * sub_total) / 100;

	// Calculate initial balance amount (before payment allocations)
	let calculated_balance = net_payable - tds_amount;

	// If this is an existing invoice, preserve the current balance_amount
	// (which may have been updated by payment allocations)
	let balance_amount = calculated_balance;
	if (!frm.doc.__islocal && frm.doc.balance_amount !== undefined) {
		// Only update balance_amount if net_payable has changed significantly
		// This preserves payment allocation updates
		let current_balance = parseFloat(frm.doc.balance_amount) || 0;
		let expected_initial_balance = net_payable - tds_amount;

		// If the current balance seems to be from payment allocations, preserve it
		if (current_balance <= expected_initial_balance) {
			balance_amount = current_balance;
		} else {
			// Net payable has changed, recalculate
			balance_amount = calculated_balance;
		}
	}

	// Update values directly without triggering events to prevent form saves
	frm.doc.retention_amount = retention_amount;
	frm.doc.total = total;
	frm.doc.cgst_amount = cgst_amount;
	frm.doc.sgst_amount = sgst_amount;
	frm.doc.igst_amount = igst_amount;
	frm.doc.net_payable = net_payable;
	frm.doc.tds_amount = tds_amount;

	// For balance_amount, use server-side calculation if this is an existing invoice
	if (!frm.doc.__islocal) {
		// Get correct balance from server considering payment allocations
		get_correct_balance_amount(frm);
	} else {
		// For new invoices, use calculated balance
		frm.doc.balance_amount = balance_amount;
	}

	// Refresh fields to show updated values
	frm.refresh_field('retention_amount');
	frm.refresh_field('total');
	frm.refresh_field('cgst_amount');
	frm.refresh_field('sgst_amount');
	frm.refresh_field('igst_amount');
	frm.refresh_field('net_payable');
	frm.refresh_field('tds_amount');
	frm.refresh_field('balance_amount');

	// Set amount in words
	frm.doc.amount_in_words = in_words(Math.round(net_payable));
	frm.refresh_field('amount_in_words');
}

// Helper function to safely parse float values and avoid NaN
function safe_float(value, default_value = 0) {
	let parsed = parseFloat(value);
	return isNaN(parsed) ? default_value : parsed;
}

// Debounce function to prevent excessive calculations
function debounce(func, wait) {
	let timeout;
	return function executedFunction(...args) {
		const later = () => {
			clearTimeout(timeout);
			func(...args);
		};
		clearTimeout(timeout);
		timeout = setTimeout(later, wait);
	};
}

// Function to calculate amount, pre_bill_amt, and curr_bill_amt for Item Rate Invoice Items
function calculate_item_rate_amounts(frm, cdt, cdn) {
	let row = locals[cdt][cdn];
	let qty = safe_float(row.qty);
	let rate = safe_float(row.rate);
	let pre_bill_qty = safe_float(row.pre_bill_qty);
	let curr_bill_qty = safe_float(row.curr_bill_qty);

	// Calculate amounts
	let new_amount = parseFloat(qty * rate).toFixed(2);
	let new_pre_bill_amt = parseFloat(rate * pre_bill_qty).toFixed(2);
	let new_curr_bill_amt = parseFloat(rate * curr_bill_qty).toFixed(2);

	// Only update if values have changed to prevent unnecessary refreshes
	if (row.amount !== new_amount) {
		row.amount = new_amount;
		frm.refresh_field(cdn, 'amount', 'item_rate_items');
	}
	if (row.pre_bill_amt !== new_pre_bill_amt) {
		row.pre_bill_amt = new_pre_bill_amt;
		frm.refresh_field(cdn, 'pre_bill_amt', 'item_rate_items');
	}
	if (row.curr_bill_amt !== new_curr_bill_amt) {
		row.curr_bill_amt = new_curr_bill_amt;
		frm.refresh_field(cdn, 'curr_bill_amt', 'item_rate_items');
	}

	// Use debounced sub-total calculation
	debouncedCalculateSubTotal(frm);
}

// Add a function to calculate sub_total
function calculate_sub_total(frm) {
    let sub_total = 0;
    if (frm.doc.contract_type === "Built Up" && frm.doc.built_up_items) {
        frm.doc.built_up_items.forEach(function(row) {
            sub_total += safe_float(row.curr_bill_amt);
        });
    } else if (frm.doc.contract_type === "Item Rate" && frm.doc.item_rate_items) {
        frm.doc.item_rate_items.forEach(function(row) {
            sub_total += safe_float(row.curr_bill_amt);
        });
    }

    // Only update if the value has actually changed to prevent unnecessary form updates
    let current_sub_total = safe_float(frm.doc.sub_total);
    if (Math.abs(current_sub_total - sub_total) > 0.01) {
        frm.doc.sub_total = sub_total; // Set directly without triggering events
        frm.refresh_field('sub_total');

        // Use debounced calculation for invoice amounts
        debouncedCalculateInvoiceAmounts(frm);
    }
}

// Function to convert number to words (Indian currency format)
function in_words(num) {
    var a = ['','one','two','three','four','five','six','seven','eight','nine','ten','eleven','twelve','thirteen','fourteen','fifteen','sixteen','seventeen','eighteen','nineteen'];
    var b = ['', '', 'twenty','thirty','forty','fifty','sixty','seventy','eighty','ninety'];

    function convert_chunk(n) {
        var s = '';
        if (n < 20) {
            s = a[n];
        } else {
            s = b[Math.floor(n / 10)] + ' ' + a[n % 10];
        }
        return s.trim();
    }

    let [integer_part, decimal_part] = String(num).split('.');
    integer_part = parseInt(integer_part) || 0;
    decimal_part = parseInt(decimal_part) || 0;

    let words = '';

    if (integer_part === 0 && decimal_part === 0) {
        return 'Rupees Zero Only';
    }

    if (integer_part > 0) {
        let n = ('000000000' + integer_part).substr(-9).match(/^(\d{2})(\d{2})(\d{2})(\d{1})(\d{2})$/);
        if (!n) return 'Error'; // Should not happen with valid numbers

        let str = '';
        str += (n[1] != 0) ? convert_chunk(Number(n[1])) + ' crore ' : '';
        str += (n[2] != 0) ? convert_chunk(Number(n[2])) + ' lakh ' : '';
        str += (n[3] != 0) ? convert_chunk(Number(n[3])) + ' thousand ' : '';
        str += (n[4] != 0) ? convert_chunk(Number(n[4])) + ' hundred ' : '';
        str += (n[5] != 0) ? convert_chunk(Number(n[5])) : '';
        words += 'Rupees ' + str.trim();
    }

    if (decimal_part > 0) {
        if (integer_part > 0) {
            words += ' and ';
        } else {
            words += 'Rupees ';
        }
        words += convert_chunk(decimal_part) + ' paise';
    }

    words += ' only';
    return words.trim().replace(/\s+/g, ' '); // Replace multiple spaces with single space
}



// Function to show the invoice selector dialog
function show_invoice_selector(frm, target_table) {
	if (!frm.doc.customer || !frm.doc.project) {
		frappe.msgprint(__("Please select Customer and Project first"));
		return;
	}
	
	frappe.call({
		method: "buildocustom.customers.doctype.client_invoice.client_invoice.get_invoices_for_customer_project",
		args: {
			customer: frm.doc.customer,
			project: frm.doc.project
		},
		callback: function(r) {
			if (r.message && r.message.length > 0) {
				show_invoice_dialog(frm, r.message, target_table);
			} else {
				frappe.msgprint(__("No invoices found for the selected Customer and Project"));
			}
		}
	});
}

// Function to display the invoice selection dialog
function show_invoice_dialog(frm, invoices, target_table) {
	const dialog = new frappe.ui.Dialog({
		title: __("Select Invoice"),
		fields: [
			{
				fieldname: "invoice",
				fieldtype: "Select",
				label: __("Invoice"),
				options: invoices.map(inv => inv.name),
				reqd: 1
			}
		],
		primary_action_label: __("Fetch Items"),
		primary_action: function() {
			const selected_invoice = dialog.get_value("invoice");
			
			frappe.call({
				method: "buildocustom.customers.doctype.client_invoice.client_invoice.get_invoice_items",
				args: {
					invoice_name: selected_invoice,
					target_table: target_table
				},
				callback: function(r) {
					if (r.message) {
						if (target_table === "built_up_items") {
							// Add items to Built Up Items table
							r.message.forEach(item => {
								const row = frm.add_child("built_up_items");
								row.hsn_code = item.hsn_code;
								row.desc = item.desc;
								row.total_perc = item.total_perc;
								row.prev_perc = item.prev_perc;
								row.curr_perc = item.curr_perc;
								row.pre_bill_amt = item.pre_bill_amt;
								row.curr_bill_amt = item.curr_bill_amt;
							});
						} else if (target_table === "item_rate_items") {
							// Add items to Item Rate Items table
							r.message.forEach(item => {
								const row = frm.add_child("item_rate_items");
								row.hsn_code = item.hsn_code;
								row.desc = item.desc;
								row.uom = item.uom;
								row.qty = item.qty;
								row.rate = item.rate;
								row.amount = item.amount;
								row.pre_bill_qty = item.pre_bill_qty;
								row.pre_bill_amt = item.pre_bill_amt;
								row.curr_bill_qty = item.curr_bill_qty;
								row.curr_bill_amt = item.curr_bill_amt;
							});
						}
						
						frm.refresh_field(target_table);
						frappe.show_alert(__("Items fetched successfully"));
					}
				}
			});
			
			dialog.hide();
		}
	});
	
	dialog.show();
}

// Event handler for the child table (Builtup Invoice Items)
frappe.ui.form.on('Builtup Invoice Items', {
	// Event handler for changes in prev_perc within a row
	prev_perc: function(frm, cdt, cdn) {
		// Use debounced calculation to prevent excessive updates while user is typing
		debouncedCalculateBuiltUpAmounts(frm, cdt, cdn);
	},
	
	// Event handler for changes in curr_perc within a row
	curr_perc: function(frm, cdt, cdn) {
		// Use debounced calculation to prevent excessive updates while user is typing
		debouncedCalculateBuiltUpAmounts(frm, cdt, cdn);
	},
	
	// Event handler when a row is added to the child table
	built_up_items_add: function(frm, cdt, cdn) {
		// Use debounced calculation for new rows as well
		debouncedCalculateBuiltUpAmounts(frm, cdt, cdn);
	}
});

// Function to calculate built-up amounts without triggering form saves
function calculate_built_up_amounts(frm, cdt, cdn) {
	let row = locals[cdt][cdn];
	let total_value = safe_float(frm.doc.total_value);

	// Calculate new amounts
	let new_pre_bill_amt, new_curr_bill_amt;

	if (total_value === 0) {
		new_pre_bill_amt = 0;
		new_curr_bill_amt = 0;
	} else {
		// Calculate pre_bill_amt
		let prev_perc = safe_float(row.prev_perc);
		new_pre_bill_amt = parseFloat((prev_perc * total_value) / 100).toFixed(2);

		// Calculate curr_bill_amt
		let curr_perc = safe_float(row.curr_perc);
		new_curr_bill_amt = parseFloat((curr_perc * total_value) / 100).toFixed(2);
	}

	// Only update if values have changed to prevent unnecessary refreshes
	if (row.pre_bill_amt !== new_pre_bill_amt) {
		row.pre_bill_amt = new_pre_bill_amt;
		frm.refresh_field(cdn, 'pre_bill_amt', 'built_up_items');
	}
	if (row.curr_bill_amt !== new_curr_bill_amt) {
		row.curr_bill_amt = new_curr_bill_amt;
		frm.refresh_field(cdn, 'curr_bill_amt', 'built_up_items');
	}

	// Add validation check - but don't reset values immediately
	let sum_perc = safe_float(row.prev_perc) + safe_float(row.curr_perc);
	let total_perc = safe_float(row.total_perc);

	if (sum_perc > total_perc) {
		// Show warning but don't reset values immediately
		frappe.show_alert({
			message: __("Warning: Sum of Previous Percentage ({0}%) and Current Percentage ({1}%) exceeds Total Percentage ({2}%)",
				[safe_float(row.prev_perc), safe_float(row.curr_perc), total_perc]),
			indicator: 'orange'
		}, 3);
	}

	// Use debounced sub-total calculation
	debouncedCalculateSubTotal(frm);
}

// Create debounced versions of calculation functions
const debouncedCalculateSubTotal = debounce(calculate_sub_total, 500);
const debouncedCalculateItemRateAmounts = debounce(calculate_item_rate_amounts, 500);
const debouncedCalculateBuiltUpAmounts = debounce(calculate_built_up_amounts, 300);
const debouncedCalculateInvoiceAmounts = debounce(calculate_invoice_amounts, 800);

// Event handler for the child table (Item Rate Invoice Items)
frappe.ui.form.on('Item Rate Invoice Items', {
	// Event handlers for changes in relevant fields
	qty: function(frm, cdt, cdn) {
		debouncedCalculateItemRateAmounts(frm, cdt, cdn);
	},
	rate: function(frm, cdt, cdn) {
		debouncedCalculateItemRateAmounts(frm, cdt, cdn);
	},
	pre_bill_qty: function(frm, cdt, cdn) {
		debouncedCalculateItemRateAmounts(frm, cdt, cdn);
	},
	curr_bill_qty: function(frm, cdt, cdn) {
		debouncedCalculateItemRateAmounts(frm, cdt, cdn);
	},
	
	// Event handler when a row is added to the child table
	item_rate_items_add: function(frm, cdt, cdn) {
		calculate_item_rate_amounts(frm, cdt, cdn); // Call the new calculation function
		calculate_sub_total(frm);
	},

	// Event handler for refreshing the child table
	refresh: function(frm, cdt, cdn) {
		if (frm.doc.item_rate_items && frm.doc.item_rate_items.length) {
			frm.doc.item_rate_items.forEach(function(row) {
				calculate_item_rate_amounts(frm, row.doctype, row.name);
			});
		}
	}
});



// Function to get correct balance amount from server
function get_correct_balance_amount(frm) {
	if (!frm.doc.name) return;

	frappe.call({
		method: "buildocustom.customers.utils.payment_allocation.recalculate_invoice_balance",
		args: {
			invoice_name: frm.doc.name
		},
		callback: function(r) {
			if (r.message !== undefined) {
				frm.set_value('balance_amount', r.message);
				frm.refresh_field('balance_amount');
			}
		}
	});
}


