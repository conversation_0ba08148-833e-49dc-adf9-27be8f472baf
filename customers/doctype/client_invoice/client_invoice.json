{"actions": [], "allow_rename": 1, "autoname": "format:{invoice_no}-{subject}-{project}-{DD}-{MM}-{YYYY}", "creation": "2025-06-18 15:32:20.070711", "doctype": "DocType", "engine": "InnoDB", "field_order": ["customer", "project", "gst_no", "pan_no", "contract_type", "column_break_odqh", "date", "invoice_no", "area", "rate", "total_value", "status", "section_break_iwcn", "subject", "site_name", "section_break_rcmq", "built_up_items", "section_break_gvtk", "item_rate_items", "section_break_mrxu", "retention_", "tds_", "cgst_", "sgst_", "igst_", "column_break_nsya", "sub_total", "retention_amount", "total", "cgst_amount", "sgst_amount", "igst_amount", "net_payable", "tds_amount", "balance_amount", "section_break_mafm", "amount_in_words", "section_break_vtqh", "bank_name", "bank_acc_no", "acc_type", "bank_branch", "ifsc_code", "pan_no_b", "gst_no_b", "column_break_wjvf", "text_editor_rnmf"], "fields": [{"fieldname": "customer", "fieldtype": "Link", "label": "Customer", "options": "Customers"}, {"fetch_from": "customer.gst_no", "fieldname": "gst_no", "fieldtype": "Data", "label": "GST NO"}, {"fetch_from": "customer.pan_no", "fieldname": "pan_no", "fieldtype": "Data", "label": "PAN No", "link_filters": "[[\"Customers\",\"pan_no\",\"=\",\"customer\"]]", "options": "Customers"}, {"fieldname": "column_break_odqh", "fieldtype": "Column Break"}, {"fieldname": "date", "fieldtype": "Date", "label": "Date"}, {"fieldname": "invoice_no", "fieldtype": "Data", "label": "Invoice No"}, {"fetch_from": "project.area", "fieldname": "area", "fieldtype": "Data", "label": "Area", "link_filters": "[[\"Projects\",\"area\",\"=\",null]]", "options": "Projects"}, {"fieldname": "project", "fieldtype": "Link", "label": "Project", "options": "Projects"}, {"fetch_from": "project.rate", "fieldname": "rate", "fieldtype": "Data", "label": "Rate", "options": "Projects"}, {"fetch_from": "project.total_value", "fieldname": "total_value", "fieldtype": "Data", "label": "Total Value", "link_filters": "[[\"Projects\",\"total_value\",\"=\",null]]", "options": "Projects"}, {"fieldname": "section_break_iwcn", "fieldtype": "Section Break"}, {"fieldname": "subject", "fieldtype": "Data", "label": "Subject"}, {"fetch_from": "project.project_name", "fieldname": "site_name", "fieldtype": "Link", "label": "Site Name", "options": "Projects"}, {"fieldname": "section_break_rcmq", "fieldtype": "Section Break"}, {"depends_on": "eval: doc.contract_type == 'Built Up'", "fieldname": "built_up_items", "fieldtype": "Table", "label": "Built Up Items", "options": "Builtup Invoice Items"}, {"fieldname": "section_break_gvtk", "fieldtype": "Section Break"}, {"depends_on": "eval: doc.contract_type == 'Item Rate'", "fieldname": "item_rate_items", "fieldtype": "Table", "label": "Item Rate Items", "options": "Item Rate Invoice Items"}, {"fieldname": "section_break_mrxu", "fieldtype": "Section Break"}, {"fieldname": "retention_", "fieldtype": "Float", "label": "Retention %"}, {"fieldname": "tds_", "fieldtype": "Float", "label": "TDS %"}, {"fieldname": "cgst_", "fieldtype": "Float", "label": "CGST %"}, {"fieldname": "sgst_", "fieldtype": "Float", "label": "SGST %"}, {"fieldname": "igst_", "fieldtype": "Float", "label": "IGST %"}, {"fieldname": "column_break_nsya", "fieldtype": "Column Break"}, {"fieldname": "sub_total", "fieldtype": "Float", "label": "Sub Total"}, {"fieldname": "retention_amount", "fieldtype": "Float", "label": "Retention Amount"}, {"fieldname": "total", "fieldtype": "Float", "label": "Total"}, {"fieldname": "cgst_amount", "fieldtype": "Float", "label": "CGST Amount"}, {"fieldname": "sgst_amount", "fieldtype": "Float", "label": "SGST Amount"}, {"fieldname": "igst_amount", "fieldtype": "Float", "label": "IGST Amount"}, {"fieldname": "net_payable", "fieldtype": "Float", "label": "Net Payable"}, {"fieldname": "tds_amount", "fieldtype": "Float", "label": "TDS Amount"}, {"fieldname": "balance_amount", "fieldtype": "Float", "label": "Balance Amount"}, {"fieldname": "section_break_mafm", "fieldtype": "Section Break"}, {"fieldname": "amount_in_words", "fieldtype": "Data", "label": "Amount In Words"}, {"fieldname": "section_break_vtqh", "fieldtype": "Section Break"}, {"fieldname": "bank_name", "fieldtype": "Data", "label": "Bank Name"}, {"fieldname": "bank_acc_no", "fieldtype": "Data", "label": "Bank Acc No"}, {"fieldname": "acc_type", "fieldtype": "Data", "label": "Acc Type"}, {"fieldname": "bank_branch", "fieldtype": "Data", "label": "Bank Branch"}, {"fieldname": "ifsc_code", "fieldtype": "Data", "label": "IFSC Code"}, {"fieldname": "pan_no_b", "fieldtype": "Data", "label": "PAN No"}, {"fieldname": "gst_no_b", "fieldtype": "Data", "label": "GST No"}, {"fieldname": "column_break_wjvf", "fieldtype": "Column Break"}, {"fieldname": "text_editor_rnmf", "fieldtype": "Text Editor"}, {"fetch_from": "project.contract_type", "fieldname": "contract_type", "fieldtype": "Data", "label": "Contract Type", "options": "Projects"}, {"default": "Not Paid", "fieldname": "status", "fieldtype": "Select", "label": "Status", "options": "Not Paid\nPartially Paid\nFully Paid"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-28 19:17:21.311557", "modified_by": "Administrator", "module": "Customers", "name": "Client Invoice", "naming_rule": "Expression (old style)", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}