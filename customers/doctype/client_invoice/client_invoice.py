# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document


class ClientInvoice(Document):
	def validate(self):
		"""Validate and calculate balance amount considering payment allocations"""
		# Only recalculate balance if this is a new document or if net_payable has changed
		if self.is_new() or self._has_net_payable_changed():
			self._calculate_balance_with_allocations()

	def _has_net_payable_changed(self):
		"""Check if net_payable has changed significantly"""
		if not self._doc_before_save:
			return True

		old_net_payable = frappe.utils.flt(self._doc_before_save.net_payable)
		new_net_payable = frappe.utils.flt(self.net_payable)

		# Consider it changed if difference is more than 0.01
		return abs(old_net_payable - new_net_payable) > 0.01

	def _calculate_balance_with_allocations(self):
		"""Calculate balance amount considering existing payment allocations"""
		from frappe.utils import flt

		# Calculate base balance (net_payable - tds_amount)
		base_balance = flt(self.net_payable) - flt(self.tds_amount)

		# If this is a new document, set balance to base balance
		if self.is_new():
			self.balance_amount = base_balance
			return

		# For existing documents, get total allocated amount
		total_allocated = frappe.db.sql("""
			SELECT COALESCE(SUM(allocated_amount), 0) as total_allocated
			FROM `tabPayment Allocation`
			WHERE client_invoice = %s
		""", (self.name,))[0][0]

		# Calculate balance considering allocations
		self.balance_amount = max(0, base_balance - flt(total_allocated))


@frappe.whitelist()
def get_invoices_for_customer_project(customer, project):
	"""
	Get all invoices for a specific customer and project
	
	Args:
		customer (str): Customer name
		project (str): Project name
		
	Returns:
		list: List of invoices
	"""
	invoices = frappe.get_all(
		"Client Invoice",
		filters={
			"customer": customer,
			"project": project,
			"docstatus": ["!=", 2]  # Not cancelled
		},
		fields=["name", "invoice_no", "date"]
	)
	
	return invoices


@frappe.whitelist()
def get_invoice_items(invoice_name, target_table):
	"""
	Get items from a specific invoice
	
	Args:
		invoice_name (str): Invoice name
		target_table (str): Target table name (built_up_items or item_rate_items)
		
	Returns:
		list: List of invoice items
	"""
	if target_table == "built_up_items":
		items = frappe.get_all(
			"Builtup Invoice Items",
			filters={"parent": invoice_name},
			fields=[
				"hsn_code", "desc", "total_perc", "prev_perc", 
				"curr_perc", "pre_bill_amt", "curr_bill_amt"
			]
		)
	elif target_table == "item_rate_items":
		items = frappe.get_all(
			"Item Rate Invoice Items",
			filters={"parent": invoice_name},
			fields=[
				"hsn_code", "desc", "uom", "qty", "rate", "amount",
				"pre_bill_qty", "pre_bill_amt", "curr_bill_qty", "curr_bill_amt"
			]
		)
	else:
		items = []
	
	return items
