// Copyright (c) 2025, <PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.ui.form.on("Payment Received", {
	refresh(frm) {


		// Show allocation status in form
		if (frm.doc.unallocated_amount > 0) {
			frm.dashboard.add_indicator(__("Unallocated Amount: {0}", [format_currency(frm.doc.unallocated_amount)]), "orange");
		} else if (frm.doc.amount > 0) {
			frm.dashboard.add_indicator(__("Fully Allocated"), "green");
		}

		// Show allocation details summary
		if (frm.doc.allocation_details && frm.doc.allocation_details.length > 0) {
			let total_allocated = frm.doc.allocation_details.reduce((sum, detail) => sum + (detail.allocated_amount || 0), 0);
			frm.dashboard.add_indicator(__("Allocated to {0} invoices: {1}", [frm.doc.allocation_details.length, format_currency(total_allocated)]), "blue");
		}
	}
});





function format_currency(amount) {
	return frappe.format(amount, {fieldtype: "Currency"});
}
