# Copyright (c) 2025, <PERSON><PERSON> and Contributors
# See license.txt

import frappe
from frappe.tests.utils import FrappeTestCase
from frappe.utils import flt
from buildocustom.customers.utils.payment_allocation import (
	allocate_payment_to_invoices,
	get_matching_invoices,
	validate_allocation_data
)


class TestPaymentReceived(FrappeTestCase):
	def setUp(self):
		"""Set up test data"""
		self.test_customer = "Test Customer"
		self.test_project = "Test Project"

		# Create test customer if not exists
		if not frappe.db.exists("Customers", self.test_customer):
			customer_doc = frappe.get_doc({
				"doctype": "Customers",
				"customer_name": self.test_customer,
				"gst_no": "TEST123456789",
				"pan_no": "TESTPAN123"
			})
			customer_doc.insert(ignore_permissions=True)

		# Create test project if not exists
		if not frappe.db.exists("Projects", self.test_project):
			project_doc = frappe.get_doc({
				"doctype": "Projects",
				"project_name": self.test_project,
				"contract_type": "Built Up"
			})
			project_doc.insert(ignore_permissions=True)

	def tearDown(self):
		"""Clean up test data"""
		# Delete test payment allocations
		frappe.db.sql("DELETE FROM `tabPayment Allocation` WHERE customer = %s", (self.test_customer,))

		# Delete test payment received records
		frappe.db.sql("DELETE FROM `tabPayment Received` WHERE customer = %s", (self.test_customer,))

		# Delete test client invoices
		frappe.db.sql("DELETE FROM `tabClient Invoice` WHERE customer = %s", (self.test_customer,))

		frappe.db.commit()

	def create_test_invoice(self, invoice_no, balance_amount, net_payable=None):
		"""Create a test client invoice"""
		if net_payable is None:
			net_payable = balance_amount

		invoice_doc = frappe.get_doc({
			"doctype": "Client Invoice",
			"customer": self.test_customer,
			"project": self.test_project,
			"invoice_no": invoice_no,
			"date": frappe.utils.today(),
			"balance_amount": balance_amount,
			"net_payable": net_payable
		})
		invoice_doc.insert(ignore_permissions=True)
		return invoice_doc

	def create_test_payment(self, amount, receipt_no):
		"""Create a test payment received"""
		payment_doc = frappe.get_doc({
			"doctype": "Payment Received",
			"customer": self.test_customer,
			"project": self.test_project,
			"receipt_no": receipt_no,
			"amount": amount,
			"date": frappe.utils.today(),
			"payment_mode": "Bank Transfer"
		})
		payment_doc.insert(ignore_permissions=True)
		return payment_doc

	def test_get_matching_invoices(self):
		"""Test getting matching invoices"""
		# Create test invoices
		invoice1 = self.create_test_invoice("INV001", 1000)
		invoice2 = self.create_test_invoice("INV002", 2000)
		invoice3 = self.create_test_invoice("INV003", 0)  # Zero balance

		# Get matching invoices
		invoices = get_matching_invoices(self.test_customer, self.test_project)

		# Should return only invoices with positive balance
		self.assertEqual(len(invoices), 2)
		invoice_names = [inv.name for inv in invoices]
		self.assertIn(invoice1.name, invoice_names)
		self.assertIn(invoice2.name, invoice_names)
		self.assertNotIn(invoice3.name, invoice_names)

	def test_payment_allocation_exact_match(self):
		"""Test payment allocation with exact amount match"""
		# Create test invoice
		invoice = self.create_test_invoice("INV001", 1000)

		# Create test payment
		payment = self.create_test_payment(1000, "PAY001")

		# Perform allocation
		result = allocate_payment_to_invoices(payment)

		# Verify allocation
		self.assertEqual(result["allocated_amount"], 1000)
		self.assertEqual(result["unallocated_amount"], 0)

		# Verify invoice balance updated
		invoice.reload()
		self.assertEqual(invoice.balance_amount, 0)

	def test_payment_allocation_partial(self):
		"""Test payment allocation with partial payment"""
		# Create test invoice
		invoice = self.create_test_invoice("INV001", 1000)

		# Create test payment (partial)
		payment = self.create_test_payment(600, "PAY001")

		# Perform allocation
		result = allocate_payment_to_invoices(payment)

		# Verify allocation
		self.assertEqual(result["allocated_amount"], 600)
		self.assertEqual(result["unallocated_amount"], 0)

		# Verify invoice balance updated
		invoice.reload()
		self.assertEqual(invoice.balance_amount, 400)

	def test_payment_allocation_overpayment(self):
		"""Test payment allocation with overpayment"""
		# Create test invoice
		invoice = self.create_test_invoice("INV001", 1000)

		# Create test payment (overpayment)
		payment = self.create_test_payment(1500, "PAY001")

		# Perform allocation
		result = allocate_payment_to_invoices(payment)

		# Verify allocation
		self.assertEqual(result["allocated_amount"], 1000)
		self.assertEqual(result["unallocated_amount"], 500)

		# Verify invoice balance updated
		invoice.reload()
		self.assertEqual(invoice.balance_amount, 0)

	def test_payment_deletion_restores_balances(self):
		"""Test that deleting payment restores invoice balances"""
		# Create test invoices
		invoice1 = self.create_test_invoice("INV001", 1000)
		invoice2 = self.create_test_invoice("INV002", 800)

		# Create test payment
		payment = self.create_test_payment(1500, "PAY001")

		# Perform allocation
		result = allocate_payment_to_invoices(payment)

		# Verify allocation occurred
		self.assertEqual(result["allocated_amount"], 1500)

		# Check invoice balances after allocation
		invoice1.reload()
		invoice2.reload()
		self.assertEqual(invoice1.balance_amount, 0)  # Fully paid
		self.assertEqual(invoice2.balance_amount, 300)  # Partially paid

		# Verify allocation records exist
		allocations_before = frappe.get_all(
			"Payment Allocation",
			filters={"payment_received": payment.name}
		)
		self.assertEqual(len(allocations_before), 2)

		# Delete the payment
		payment.delete()

		# Verify invoice balances are restored
		invoice1.reload()
		invoice2.reload()
		self.assertEqual(invoice1.balance_amount, 1000)  # Restored
		self.assertEqual(invoice2.balance_amount, 800)   # Restored

		# Verify allocation records are deleted
		allocations_after = frappe.get_all(
			"Payment Allocation",
			filters={"payment_received": payment.name}
		)
		self.assertEqual(len(allocations_after), 0)

	def test_partial_payment_deletion(self):
		"""Test deleting partial payment restores correct balance"""
		# Create test invoice
		invoice = self.create_test_invoice("INV001", 1000)

		# Create partial payment
		payment = self.create_test_payment(600, "PAY001")

		# Perform allocation
		allocate_payment_to_invoices(payment)

		# Verify partial allocation
		invoice.reload()
		self.assertEqual(invoice.balance_amount, 400)

		# Delete payment
		payment.delete()

		# Verify balance is fully restored
		invoice.reload()
		self.assertEqual(invoice.balance_amount, 1000)

	def test_multiple_payments_deletion(self):
		"""Test deleting one of multiple payments for same invoice"""
		# Create test invoice
		invoice = self.create_test_invoice("INV001", 1000)

		# Create first payment
		payment1 = self.create_test_payment(400, "PAY001")
		allocate_payment_to_invoices(payment1)

		# Create second payment
		payment2 = self.create_test_payment(300, "PAY002")
		allocate_payment_to_invoices(payment2)

		# Verify both payments allocated
		invoice.reload()
		self.assertEqual(invoice.balance_amount, 300)  # 1000 - 400 - 300

		# Delete first payment only
		payment1.delete()

		# Verify only first payment's allocation is reversed
		invoice.reload()
		self.assertEqual(invoice.balance_amount, 700)  # 1000 - 300 (second payment remains)

		# Verify second payment's allocation still exists
		remaining_allocations = frappe.get_all(
			"Payment Allocation",
			filters={"payment_received": payment2.name}
		)
		self.assertEqual(len(remaining_allocations), 1)
