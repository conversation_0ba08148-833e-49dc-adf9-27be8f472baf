# Automatic Payment Allocation System

## Overview

The Automatic Payment Allocation System provides seamless allocation of payments received against outstanding client invoices. The system automatically matches payments to invoices based on customer and project, using a First-In-First-Out (FIFO) allocation method.

## Key Features

- **Automatic Matching**: Finds all Client Invoice records matching the same customer and project
- **FIFO Allocation**: Allocates payments to oldest invoices first
- **Balance Management**: Updates invoice balance amounts automatically
- **Audit Trail**: Maintains complete allocation history via Payment Allocation doctype
- **Overpayment Handling**: Tracks unallocated amounts when payments exceed outstanding invoices
- **Error Handling**: Comprehensive validation and transaction rollback on failures

## System Components

### 1. Payment Allocation Doctype
- **Purpose**: Audit trail for all payment allocations
- **Fields**:
  - Payment Received (Link)
  - Client Invoice (Link)
  - Customer (Link, read-only)
  - Project (Link, read-only)
  - Allocated Amount (Currency)
  - Allocation Date (Datetime)
  - Allocation Method (Select: FIFO/Proportional/Manual)
  - Remarks (Text)

### 2. Enhanced Payment Received Doctype
- **New Field**: `unallocated_amount` - tracks remaining payment after allocation
- **Automatic Processing**: Triggers allocation on save

### 3. Payment Allocation Utility Module
- **Location**: `buildocustom.customers.utils.payment_allocation`
- **Core Functions**:
  - `allocate_payment_to_invoices()` - Main allocation logic
  - `get_matching_invoices()` - Find eligible invoices
  - `perform_fifo_allocation()` - Execute FIFO allocation
  - `create_payment_allocation()` - Create audit records

## Allocation Logic

### FIFO Method
1. **Find Matching Invoices**: Query Client Invoice records with:
   - Same customer and project
   - Balance amount > 0
   - Not cancelled (docstatus != 2)
   - Ordered by date (oldest first)

2. **Allocate Payment**: For each invoice in order:
   - Calculate allocation amount = min(remaining_payment, invoice_balance)
   - Create Payment Allocation record
   - Update invoice balance_amount
   - Reduce remaining payment amount

3. **Handle Remaining Amount**: 
   - Update Payment Received.unallocated_amount
   - If > 0, indicates overpayment

### Validation Rules
- **Balance Protection**: Invoice balance cannot go below zero
- **Payment Validation**: Payment amount must be > 0
- **Required Fields**: Customer and project must be specified
- **Data Integrity**: Transaction rollback on any allocation failure

## Usage Examples

### Scenario 1: Exact Payment Match
```
Invoice INV-001: Balance ₹10,000
Payment PAY-001: Amount ₹10,000
Result: 
- INV-001 balance: ₹0
- PAY-001 unallocated: ₹0
```

### Scenario 2: Partial Payment
```
Invoice INV-001: Balance ₹10,000
Payment PAY-001: Amount ₹6,000
Result:
- INV-001 balance: ₹4,000
- PAY-001 unallocated: ₹0
```

### Scenario 3: Overpayment
```
Invoice INV-001: Balance ₹10,000
Payment PAY-001: Amount ₹15,000
Result:
- INV-001 balance: ₹0
- PAY-001 unallocated: ₹5,000
```

### Scenario 4: Multiple Invoices (FIFO)
```
Invoice INV-001 (2025-01-01): Balance ₹8,000
Invoice INV-002 (2025-01-05): Balance ₹5,000
Payment PAY-001: Amount ₹10,000
Result:
- INV-001 balance: ₹0 (fully paid first)
- INV-002 balance: ₹3,000 (partially paid)
- PAY-001 unallocated: ₹0
```

## API Functions

### Core Functions

#### `allocate_payment_to_invoices(payment_doc)`
Main allocation function called automatically on payment save.

**Parameters:**
- `payment_doc`: Payment Received document

**Returns:**
```python
{
    "allocated_amount": 10000.0,
    "unallocated_amount": 0.0,
    "allocations": [
        {
            "invoice": "INV-001",
            "amount": 10000.0,
            "allocation_id": "PAL-2025-06-20-00001"
        }
    ]
}
```

#### `get_allocation_summary(payment_name)`
Get complete allocation summary for a payment.

**Parameters:**
- `payment_name`: Payment Received name

**Returns:**
```python
{
    "total_amount": 15000.0,
    "allocated_amount": 10000.0,
    "unallocated_amount": 5000.0,
    "allocations": [...]
}
```

#### `manual_reallocate_payment(payment_name)`
Manually trigger reallocation of a payment.

### Utility Functions

#### `get_customer_project_outstanding(customer, project)`
Get total outstanding amount for customer-project combination.

#### `get_payment_allocation_report(filters)`
Generate allocation report with optional filters.

#### `recalculate_invoice_balance(invoice_name)`
Recalculate invoice balance based on allocations.

## Error Handling

### Transaction Safety
- Database transactions ensure data consistency
- Automatic rollback on allocation failures
- Comprehensive error logging

### Validation Checks
- Negative balance prevention
- Required field validation
- Data type validation
- Existence checks for linked documents

### Error Recovery
- Failed allocations are logged but don't stop processing
- Partial allocation results are preserved
- Manual reallocation available for error correction

## Configuration

### Hooks Configuration
```python
# hooks.py
doc_events = {
    "Payment Received": {
        "on_save": "buildocustom.customers.utils.payment_allocation.handle_payment_allocation_on_save",
        "on_trash": "buildocustom.customers.utils.payment_allocation.handle_payment_allocation_on_delete"
    }
}
```

### Permissions
- System Manager: Full access to all allocation functions
- Custom roles can be configured for specific allocation operations

## Testing

### Unit Tests
- Test file: `test_payment_received.py`
- Coverage includes:
  - FIFO allocation logic
  - Overpayment handling
  - Validation errors
  - Multiple invoice scenarios

### Manual Testing
1. Create Client Invoices with outstanding balances
2. Create Payment Received with matching customer/project
3. Verify automatic allocation and balance updates
4. Check Payment Allocation audit records

## Troubleshooting

### Common Issues

1. **No Allocation Occurring**
   - Check customer/project match between payment and invoices
   - Verify invoices have positive balance_amount
   - Check for validation errors in logs

2. **Incorrect Balance Updates**
   - Use `recalculate_invoice_balance()` function
   - Check for duplicate allocation records
   - Verify transaction completion

3. **Performance Issues**
   - Monitor allocation processing time
   - Consider indexing on customer/project fields
   - Review large payment batch processing

### Debugging
- Check Error Log for allocation errors
- Review Payment Allocation records for audit trail
- Use allocation summary functions for verification

## Future Enhancements

- Proportional allocation method
- Custom allocation rules
- Bulk payment processing
- Integration with accounting modules
- Advanced reporting and analytics
