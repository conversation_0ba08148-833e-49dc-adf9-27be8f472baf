# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.utils import flt, getdate, formatdate
from frappe import _


def execute(filters=None):
	"""
	Main execution function for Supplier Ledger report

	Args:
		filters (dict): Report filters containing project, supplier, from_date, to_date

	Returns:
		tuple: (columns, data, message, chart, report_summary) for the report
	"""
	if not filters:
		filters = {}

	# No validation required - show all data if no filters selected

	# Get columns definition
	columns = get_columns(filters)

	# Get report data
	data = get_ledger_data(filters)

	# Calculate summary data for cards
	summary_data = calculate_summary_data(data)

	# Create report summary cards
	report_summary = get_report_summary(summary_data)

	return columns, data, None, None, report_summary


def get_columns(filters):
	"""
	Define columns for the Supplier Ledger report

	Args:
		filters (dict): Report filters

	Returns:
		list: Column definitions for the report
	"""
	columns = [
		{
			"label": _("Date"),
			"fieldname": "date",
			"fieldtype": "Date",
			"width": 100
		},
		{
			"label": _("Type"),
			"fieldname": "type",
			"fieldtype": "Data",
			"width": 100
		}
	]

	# Add Supplier column if no specific supplier is selected
	if not filters or not filters.get("supplier"):
		columns.append({
			"label": _("Supplier"),
			"fieldname": "supplier",
			"fieldtype": "Link",
			"options": "Suppliers",
			"width": 150
		})

	# Add Project column if no specific project is selected
	if not filters or not filters.get("project"):
		columns.append({
			"label": _("Project"),
			"fieldname": "project",
			"fieldtype": "Link",
			"options": "Projects",
			"width": 150
		})

	# Add remaining columns
	columns.extend([
		{
			"label": _("Invoice / Payment No"),
			"fieldname": "reference",
			"fieldtype": "Data",
			"width": 170
		},
		{
			"label": _("Debit"),
			"fieldname": "debit",
			"fieldtype": "Currency",
			"width": 120
		},
		{
			"label": _("Credit"),
			"fieldname": "credit",
			"fieldtype": "Currency",
			"width": 120
		},
		{
			"label": _("Balance"),
			"fieldname": "balance",
			"fieldtype": "Currency",
			"width": 120
		}
	])

	return columns


def get_ledger_data(filters):
	"""
	Get combined ledger data from supplier invoices and payments paid

	Args:
		filters (dict): Report filters

	Returns:
		list: Combined and sorted transaction data
	"""
	# Get supplier invoice transactions (debits)
	invoice_transactions = get_supplier_invoice_transactions(filters)

	# Get payment transactions (credits)
	payment_transactions = get_payment_transactions(filters)

	# Combine all transactions
	all_transactions = invoice_transactions + payment_transactions

	# Sort by date and creation time
	all_transactions.sort(key=lambda x: (x['date'], x.get('creation_time', '')))

	# Calculate running balance
	running_balance = 0
	for transaction in all_transactions:
		debit = flt(transaction.get('debit', 0))
		credit = flt(transaction.get('credit', 0))
		running_balance += debit - credit
		transaction['balance'] = running_balance

	return all_transactions


def get_supplier_invoice_transactions(filters):
	"""
	Get supplier invoice transactions (debit entries)

	Args:
		filters (dict): Report filters

	Returns:
		list: Supplier invoice transactions
	"""
	conditions = []
	values = []

	# Apply filters
	if filters.get("supplier"):
		conditions.append("supplier = %s")
		values.append(filters.get("supplier"))

	if filters.get("project"):
		conditions.append("project = %s")
		values.append(filters.get("project"))

	if filters.get("from_date"):
		conditions.append("date >= %s")
		values.append(filters.get("from_date"))

	if filters.get("to_date"):
		conditions.append("date <= %s")
		values.append(filters.get("to_date"))

	# Only non-cancelled invoices
	conditions.append("docstatus != 2")

	where_clause = " AND ".join(conditions) if conditions else "1=1"

	query = f"""
		SELECT
			date,
			name,
			supplier_invoice_no,
			grand_total,
			supplier,
			project,
			creation
		FROM `tabSupplier Invoices`
		WHERE {where_clause}
		ORDER BY date ASC, creation ASC
	"""

	invoices = frappe.db.sql(query, values, as_dict=True)

	transactions = []
	for invoice in invoices:
		# Use grand_total as debit amount
		debit_amount = flt(invoice.grand_total)

		# Only include if debit amount is positive
		if debit_amount > 0:
			# Create descriptive reference
			reference = invoice.supplier_invoice_no or invoice.name
			if invoice.supplier_invoice_no and invoice.supplier_invoice_no != invoice.name:
				reference = f"{invoice.supplier_invoice_no} ({invoice.name})"

			transactions.append({
				'date': invoice.date,
				'type': 'Bill',
				'supplier': invoice.supplier,
				'project': invoice.project,
				'reference': reference,
				'debit': debit_amount,
				'credit': 0,
				'invoice_name': invoice.name,
				'creation_time': invoice.creation
			})

	return transactions


def get_payment_transactions(filters):
	"""
	Get payment transactions (credit entries)

	Args:
		filters (dict): Report filters

	Returns:
		list: Payment transactions
	"""
	conditions = []
	values = []

	# Apply filters
	if filters.get("supplier"):
		conditions.append("supplier = %s")
		values.append(filters.get("supplier"))

	if filters.get("project"):
		conditions.append("project = %s")
		values.append(filters.get("project"))

	if filters.get("from_date"):
		conditions.append("date >= %s")
		values.append(filters.get("from_date"))

	if filters.get("to_date"):
		conditions.append("date <= %s")
		values.append(filters.get("to_date"))

	# Only non-cancelled payments
	conditions.append("docstatus != 2")

	where_clause = " AND ".join(conditions) if conditions else "1=1"

	query = f"""
		SELECT
			date,
			name,
			payment_id_no,
			amount,
			supplier,
			project,
			payment_mode,
			reference_no,
			creation
		FROM `tabPayments Paid`
		WHERE {where_clause}
		ORDER BY date ASC, creation ASC
	"""

	payments = frappe.db.sql(query, values, as_dict=True)

	transactions = []
	for payment in payments:
		credit_amount = flt(payment.amount)

		# Only include if credit amount is positive
		if credit_amount > 0:
			# Create descriptive reference
			reference = payment.payment_id_no or payment.name
			if payment.payment_id_no and payment.payment_id_no != payment.name:
				reference = f"{payment.payment_id_no} ({payment.name})"

			transactions.append({
				'date': payment.date,
				'type': 'Payment',
				'supplier': payment.supplier,
				'project': payment.project,
				'reference': reference,
				'debit': 0,
				'credit': credit_amount,
				'payment_name': payment.name,
				'creation_time': payment.creation
			})

	return transactions


def calculate_summary_data(data):
	"""
	Calculate summary data for report cards

	Args:
		data (list): Report data

	Returns:
		dict: Summary data
	"""
	total_debit = sum(flt(row.get('debit', 0)) for row in data)
	total_credit = sum(flt(row.get('credit', 0)) for row in data)
	net_balance = total_debit - total_credit

	return {
		'total_debit': total_debit,
		'total_credit': total_credit,
		'net_balance': net_balance,
		'total_transactions': len(data)
	}


def get_report_summary(summary_data):
	"""
	Create report summary cards

	Args:
		summary_data (dict): Summary data

	Returns:
		list: Report summary cards
	"""
	return [
		{
			"value": summary_data['total_transactions'],
			"label": "Total Transactions",
			"datatype": "Int",
			"indicator": "Blue"
		},
		{
			"value": summary_data['total_debit'],
			"label": "Total Bills",
			"datatype": "Currency",
			"indicator": "Red"
		},
		{
			"value": summary_data['total_credit'],
			"label": "Total Payments",
			"datatype": "Currency",
			"indicator": "Green"
		},
		{
			"value": summary_data['net_balance'],
			"label": "Net Balance",
			"datatype": "Currency",
			"indicator": "Blue"
		}
	]
