{"actions": [], "allow_rename": 1, "autoname": "format:SPA-{YYYY}-{MM}-{DD}-{#####}", "creation": "2025-06-20 14:00:00.000000", "doctype": "DocType", "engine": "InnoDB", "field_order": ["payments_paid", "supplier_invoice", "supplier", "project", "column_break_1", "allocation_date", "allocated_amount", "allocation_method", "section_break_2", "remarks"], "fields": [{"fieldname": "payments_paid", "fieldtype": "Link", "label": "Payments Paid", "options": "Payments Paid", "reqd": 1}, {"fieldname": "supplier_invoice", "fieldtype": "Link", "label": "Supplier Invoice", "options": "Supplier Invoices", "reqd": 1}, {"fieldname": "supplier", "fieldtype": "Link", "label": "Supplier", "options": "Suppliers", "read_only": 1}, {"fieldname": "project", "fieldtype": "Link", "label": "Project", "options": "Projects", "read_only": 1}, {"fieldname": "column_break_1", "fieldtype": "Column Break"}, {"fieldname": "allocation_date", "fieldtype": "Datetime", "label": "Allocation Date", "read_only": 1, "reqd": 1}, {"fieldname": "allocated_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Allocated Amount", "precision": "2", "reqd": 1}, {"default": "FIFO", "fieldname": "allocation_method", "fieldtype": "Select", "label": "Allocation Method", "options": "FIFO\nProportional\nManual", "read_only": 1}, {"fieldname": "section_break_2", "fieldtype": "Section Break"}, {"fieldname": "remarks", "fieldtype": "Text", "label": "Remarks"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-06-20 14:00:00.000000", "modified_by": "Administrator", "module": "Suppliers", "name": "Supplier Payment Allocation", "naming_rule": "Expression (old style)", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}