# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document


class SupplierPaymentAllocation(Document):
	def before_save(self):
		"""Set supplier and project from payments paid"""
		if self.payments_paid:
			payment_doc = frappe.get_doc("Payments Paid", self.payments_paid)
			self.supplier = payment_doc.supplier
			self.project = payment_doc.project

	def validate(self):
		"""Validate allocation data"""
		if self.allocated_amount <= 0:
			frappe.throw("Allocated amount must be greater than zero")
		
		# Validate that supplier and project match between payment and invoice
		if self.payments_paid and self.supplier_invoice:
			payment_doc = frappe.get_doc("Payments Paid", self.payments_paid)
			invoice_doc = frappe.get_doc("Supplier Invoices", self.supplier_invoice)
			
			if payment_doc.supplier != invoice_doc.supplier:
				frappe.throw("Supplier mismatch between payment and invoice")
			
			if payment_doc.project != invoice_doc.project:
				frappe.throw("Project mismatch between payment and invoice")
