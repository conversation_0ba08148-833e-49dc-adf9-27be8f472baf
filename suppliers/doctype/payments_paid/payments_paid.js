// Copyright (c) 2025, <PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.ui.form.on("Payments Paid", {
	refresh(frm) {


		// Show unallocated amount indicator
		if (frm.doc.unallocated_amount && frm.doc.unallocated_amount > 0) {
			frm.dashboard.add_indicator(__("Unallocated Amount: {0}", [format_currency(frm.doc.unallocated_amount)]), "orange");
		}
	}
});

// Function to show allocation summary for this payment
function show_supplier_allocation_summary(frm) {
	frappe.call({
		method: "frappe.client.get_list",
		args: {
			doctype: "Supplier Payment Allocation",
			filters: {
				payments_paid: frm.doc.name
			},
			fields: ["supplier_invoice", "allocated_amount", "allocation_date", "allocation_method", "remarks"]
		},
		callback: function(r) {
			if (r.message && r.message.length > 0) {
				let total_allocated = r.message.reduce((sum, alloc) => sum + (alloc.allocated_amount || 0), 0);

				let html = `
					<div class="allocation-summary">
						<h4>Payment Allocation Summary</h4>
						<p><strong>Total Payment:</strong> ${format_currency(frm.doc.amount)}</p>
						<p><strong>Total Allocated:</strong> ${format_currency(total_allocated)}</p>
						<p><strong>Unallocated:</strong> ${format_currency(frm.doc.unallocated_amount || 0)}</p>
						<hr>
						<table class="table table-bordered">
							<thead>
								<tr>
									<th>Supplier Invoice</th>
									<th>Allocated Amount</th>
									<th>Allocation Date</th>
									<th>Method</th>
								</tr>
							</thead>
							<tbody>
				`;

				r.message.forEach(function(alloc) {
					html += `
						<tr>
							<td><a href="/app/supplier-invoices/${alloc.supplier_invoice}">${alloc.supplier_invoice}</a></td>
							<td>${format_currency(alloc.allocated_amount)}</td>
							<td>${frappe.datetime.str_to_user(alloc.allocation_date)}</td>
							<td>${alloc.allocation_method}</td>
						</tr>
					`;
				});

				html += `
							</tbody>
						</table>
					</div>
				`;

				frappe.msgprint({
					title: __("Payment Allocation Summary"),
					message: html,
					wide: true
				});
			} else {
				frappe.msgprint(__("No allocations found for this payment."));
			}
		}
	});
}

// Function to reallocate payment
function reallocate_supplier_payment(frm) {
	frappe.confirm(
		__("This will clear existing allocations and reallocate the payment. Continue?"),
		function() {
			frappe.call({
				method: "buildocustom.suppliers.utils.supplier_payment_allocation.manual_reallocate_supplier_payment",
				args: {
					payment_name: frm.doc.name
				},
				callback: function(r) {
					if (r.message) {
						frappe.msgprint({
							title: __("Reallocation Complete"),
							message: __("Allocated: {0}, Unallocated: {1}", [
								format_currency(r.message.allocated_amount),
								format_currency(r.message.unallocated_amount)
							])
						});
						frm.reload_doc();
					}
				}
			});
		}
	);
}

// Function to show outstanding supplier invoices
function show_outstanding_supplier_invoices(frm) {
	if (!frm.doc.supplier || !frm.doc.project) {
		frappe.msgprint(__("Supplier and Project are required to show outstanding invoices."));
		return;
	}

	frappe.call({
		method: "frappe.client.get_list",
		args: {
			doctype: "Supplier Invoices",
			filters: {
				supplier: frm.doc.supplier,
				project: frm.doc.project,
				balance_amount: [">", 0]
			},
			fields: ["name", "supplier_invoice_no", "date", "grand_total", "balance_amount", "status"],
			order_by: "date asc"
		},
		callback: function(r) {
			if (r.message && r.message.length > 0) {
				let total_outstanding = r.message.reduce((sum, inv) => sum + (inv.balance_amount || 0), 0);

				let html = `
					<div class="outstanding-invoices">
						<h4>Outstanding Supplier Invoices</h4>
						<p><strong>Supplier:</strong> ${frm.doc.supplier}</p>
						<p><strong>Project:</strong> ${frm.doc.project}</p>
						<p><strong>Total Outstanding:</strong> ${format_currency(total_outstanding)}</p>
						<hr>
						<table class="table table-bordered">
							<thead>
								<tr>
									<th>Invoice No</th>
									<th>Date</th>
									<th>Grand Total</th>
									<th>Balance Amount</th>
									<th>Status</th>
								</tr>
							</thead>
							<tbody>
				`;

				r.message.forEach(function(inv) {
					html += `
						<tr>
							<td><a href="/app/supplier-invoices/${inv.name}">${inv.supplier_invoice_no}</a></td>
							<td>${frappe.datetime.str_to_user(inv.date)}</td>
							<td>${format_currency(inv.grand_total)}</td>
							<td>${format_currency(inv.balance_amount)}</td>
							<td><span class="indicator ${inv.status === 'Paid' ? 'green' : inv.status === 'Partially Paid' ? 'orange' : 'red'}">${inv.status}</span></td>
						</tr>
					`;
				});

				html += `
							</tbody>
						</table>
					</div>
				`;

				frappe.msgprint({
					title: __("Outstanding Supplier Invoices"),
					message: html,
					wide: true
				});
			} else {
				frappe.msgprint(__("No outstanding invoices found for this supplier and project."));
			}
		}
	});
}
