# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.tests.utils import FrappeTestCase
from frappe.utils import flt, today
from buildocustom.suppliers.utils.supplier_payment_allocation import (
    allocate_payment_to_supplier_invoices,
    get_matching_supplier_invoices,
    clear_supplier_payment_allocations
)


class TestSupplierPaymentAllocation(FrappeTestCase):
    def setUp(self):
        """Set up test data"""
        self.cleanup_test_data()
        
        # Create test supplier
        self.supplier = frappe.get_doc({
            "doctype": "Suppliers",
            "supplier_name": "Test Supplier",
            "supplier_type": "Company"
        }).insert(ignore_permissions=True)
        
        # Create test project
        self.project = frappe.get_doc({
            "doctype": "Projects",
            "project_name": "Test Project",
            "project_type": "Internal"
        }).insert(ignore_permissions=True)
        
        # Create test supplier invoices
        self.invoice1 = self.create_test_invoice("INV-001", 1000, 800)  # 800 balance
        self.invoice2 = self.create_test_invoice("INV-002", 1500, 1200)  # 1200 balance
        self.invoice3 = self.create_test_invoice("INV-003", 500, 0)  # 0 balance (fully paid)
        
    def tearDown(self):
        """Clean up test data"""
        self.cleanup_test_data()
        
    def cleanup_test_data(self):
        """Remove test data"""
        # Delete allocations
        frappe.db.delete("Supplier Payment Allocation", {"supplier": ["like", "Test Supplier%"]})
        
        # Delete payments
        frappe.db.delete("Payments Paid", {"supplier": ["like", "Test Supplier%"]})
        
        # Delete invoices
        frappe.db.delete("Supplier Invoices", {"supplier": ["like", "Test Supplier%"]})
        
        # Delete supplier and project
        frappe.db.delete("Suppliers", {"supplier_name": ["like", "Test Supplier%"]})
        frappe.db.delete("Projects", {"project_name": ["like", "Test Project%"]})
        
        frappe.db.commit()
        
    def create_test_invoice(self, invoice_no, grand_total, balance_amount):
        """Create a test supplier invoice"""
        invoice = frappe.get_doc({
            "doctype": "Supplier Invoices",
            "supplier": self.supplier.name,
            "project": self.project.name,
            "supplier_invoice_no": invoice_no,
            "date": today(),
            "grand_total": grand_total,
            "balance_amount": balance_amount,
            "status": "Not Paid" if balance_amount > 0 else "Paid"
        })
        invoice.insert(ignore_permissions=True)
        return invoice
        
    def create_test_payment(self, payment_id, amount):
        """Create a test payment"""
        payment = frappe.get_doc({
            "doctype": "Payments Paid",
            "payment_id_no": payment_id,
            "supplier": self.supplier.name,
            "project": self.project.name,
            "date": today(),
            "amount": amount,
            "payment_mode": "Bank Transfer"
        })
        payment.insert(ignore_permissions=True)
        return payment
        
    def test_get_matching_invoices(self):
        """Test getting matching invoices with outstanding balance"""
        invoices = get_matching_supplier_invoices(self.supplier.name, self.project.name)
        
        # Should return only invoices with balance > 0, ordered by date
        self.assertEqual(len(invoices), 2)  # invoice1 and invoice2, not invoice3
        self.assertEqual(invoices[0].name, self.invoice1.name)  # FIFO order
        self.assertEqual(invoices[1].name, self.invoice2.name)
        
    def test_partial_allocation(self):
        """Test partial payment allocation"""
        # Create payment that partially covers first invoice
        payment = self.create_test_payment("PAY-001", 500)
        
        # Allocate payment
        result = allocate_payment_to_supplier_invoices(payment)
        
        # Verify allocation result
        self.assertEqual(result["allocated_amount"], 500)
        self.assertEqual(result["unallocated_amount"], 0)
        self.assertEqual(len(result["allocations"]), 1)
        
        # Verify invoice balance updated
        self.invoice1.reload()
        self.assertEqual(flt(self.invoice1.balance_amount), 300)  # 800 - 500
        self.assertEqual(self.invoice1.status, "Partially Paid")
        
        # Verify allocation record created
        allocations = frappe.get_all("Supplier Payment Allocation", 
                                   filters={"payments_paid": payment.name})
        self.assertEqual(len(allocations), 1)
        
    def test_full_allocation_single_invoice(self):
        """Test full payment allocation to single invoice"""
        # Create payment that fully covers first invoice
        payment = self.create_test_payment("PAY-002", 800)
        
        # Allocate payment
        result = allocate_payment_to_supplier_invoices(payment)
        
        # Verify allocation result
        self.assertEqual(result["allocated_amount"], 800)
        self.assertEqual(result["unallocated_amount"], 0)
        
        # Verify invoice balance and status
        self.invoice1.reload()
        self.assertEqual(flt(self.invoice1.balance_amount), 0)
        self.assertEqual(self.invoice1.status, "Paid")
        
    def test_fifo_allocation_multiple_invoices(self):
        """Test FIFO allocation across multiple invoices"""
        # Create payment that covers first invoice and part of second
        payment = self.create_test_payment("PAY-003", 1500)  # 800 + 700
        
        # Allocate payment
        result = allocate_payment_to_supplier_invoices(payment)
        
        # Verify allocation result
        self.assertEqual(result["allocated_amount"], 1500)
        self.assertEqual(result["unallocated_amount"], 0)
        self.assertEqual(len(result["allocations"]), 2)
        
        # Verify first invoice fully paid
        self.invoice1.reload()
        self.assertEqual(flt(self.invoice1.balance_amount), 0)
        self.assertEqual(self.invoice1.status, "Paid")
        
        # Verify second invoice partially paid
        self.invoice2.reload()
        self.assertEqual(flt(self.invoice2.balance_amount), 500)  # 1200 - 700
        self.assertEqual(self.invoice2.status, "Partially Paid")
        
    def test_overpayment_handling(self):
        """Test handling of overpayment (unallocated amount)"""
        # Create payment larger than total outstanding
        total_outstanding = 800 + 1200  # invoice1 + invoice2
        payment = self.create_test_payment("PAY-004", total_outstanding + 500)
        
        # Allocate payment
        result = allocate_payment_to_supplier_invoices(payment)
        
        # Verify allocation result
        self.assertEqual(result["allocated_amount"], total_outstanding)
        self.assertEqual(result["unallocated_amount"], 500)
        
        # Verify both invoices fully paid
        self.invoice1.reload()
        self.invoice2.reload()
        self.assertEqual(flt(self.invoice1.balance_amount), 0)
        self.assertEqual(flt(self.invoice2.balance_amount), 0)
        self.assertEqual(self.invoice1.status, "Paid")
        self.assertEqual(self.invoice2.status, "Paid")
        
    def test_clear_allocations(self):
        """Test clearing payment allocations"""
        # Create and allocate payment
        payment = self.create_test_payment("PAY-005", 1000)
        allocate_payment_to_supplier_invoices(payment)
        
        # Verify allocation exists
        allocations_before = frappe.get_all("Supplier Payment Allocation", 
                                          filters={"payments_paid": payment.name})
        self.assertGreater(len(allocations_before), 0)
        
        # Clear allocations
        clear_supplier_payment_allocations(payment.name)
        
        # Verify allocations cleared
        allocations_after = frappe.get_all("Supplier Payment Allocation", 
                                         filters={"payments_paid": payment.name})
        self.assertEqual(len(allocations_after), 0)
        
        # Verify invoice balances restored
        self.invoice1.reload()
        self.assertEqual(flt(self.invoice1.balance_amount), 800)  # Original balance
        
    def test_no_matching_invoices(self):
        """Test allocation when no matching invoices exist"""
        # Create different supplier
        other_supplier = frappe.get_doc({
            "doctype": "Suppliers",
            "supplier_name": "Other Supplier",
            "supplier_type": "Company"
        }).insert(ignore_permissions=True)
        
        # Create payment for different supplier
        payment = frappe.get_doc({
            "doctype": "Payments Paid",
            "payment_id_no": "PAY-006",
            "supplier": other_supplier.name,
            "project": self.project.name,
            "date": today(),
            "amount": 1000,
            "payment_mode": "Bank Transfer"
        })
        payment.insert(ignore_permissions=True)
        
        # Allocate payment
        result = allocate_payment_to_supplier_invoices(payment)
        
        # Verify no allocation
        self.assertEqual(result["allocated_amount"], 0)
        self.assertEqual(result["unallocated_amount"], 1000)
        
        # Clean up
        frappe.delete_doc("Payments Paid", payment.name, ignore_permissions=True)
        frappe.delete_doc("Suppliers", other_supplier.name, ignore_permissions=True)


if __name__ == "__main__":
    import unittest
    unittest.main()
