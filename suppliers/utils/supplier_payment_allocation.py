# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe import _
from frappe.utils import flt, now


def allocate_payment_to_supplier_invoices(payment_doc):
	"""
	Automatically allocate payment to matching supplier invoices using FIFO method

	Args:
		payment_doc: Payments Paid document

	Returns:
		dict: Allocation summary with total allocated amount and unallocated amount
	"""
	try:
		# Validate input data
		if not payment_doc.supplier or not payment_doc.project or not payment_doc.amount:
			return {"allocated_amount": 0, "unallocated_amount": payment_doc.amount or 0}

		# Additional validation
		if flt(payment_doc.amount) <= 0:
			frappe.throw(_("Payment amount must be greater than zero"))

		# Find matching invoices with outstanding balance
		matching_invoices = get_matching_supplier_invoices(payment_doc.supplier, payment_doc.project)

		if not matching_invoices:
			return {"allocated_amount": 0, "unallocated_amount": payment_doc.amount}

		# Start database transaction
		frappe.db.begin()

		try:
			# Perform allocation using FIFO method
			allocation_result = perform_fifo_allocation_supplier(payment_doc, matching_invoices)

			# Commit transaction if successful
			frappe.db.commit()

			return allocation_result

		except Exception as e:
			# Rollback transaction on error
			frappe.db.rollback()
			frappe.log_error(f"Error in FIFO allocation: {str(e)}", "Supplier Payment Allocation Error")
			raise

	except Exception as e:
		frappe.log_error(f"Error in supplier payment allocation: {str(e)}", "Supplier Payment Allocation Error")
		return {"allocated_amount": 0, "unallocated_amount": payment_doc.amount or 0, "error": str(e)}


def get_matching_supplier_invoices(supplier, project):
	"""
	Get supplier invoices that match the supplier and project with outstanding balance

	Args:
		supplier (str): Supplier name
		project (str): Project name

	Returns:
		list: List of matching supplier invoices ordered by date (FIFO)
	"""
	try:
		# Query for matching invoices with balance > 0, ordered by date (oldest first)
		invoices = frappe.get_all(
			"Supplier Invoices",
			filters={
				"supplier": supplier,
				"project": project,
				"balance_amount": [">", 0],
				"docstatus": ["!=", 2]  # Not cancelled
			},
			fields=["name", "balance_amount", "date", "supplier_invoice_no", "grand_total"],
			order_by="date asc, creation asc"  # FIFO: oldest first
		)

		# Additional validation to ensure balance_amount is actually > 0
		valid_invoices = []
		for invoice in invoices:
			if flt(invoice.balance_amount) > 0:
				valid_invoices.append(invoice)

		return valid_invoices

	except Exception as e:
		frappe.log_error(f"Error fetching matching supplier invoices: {str(e)}", "Supplier Payment Allocation Error")
		return []


def perform_fifo_allocation_supplier(payment_doc, invoices):
	"""
	Perform FIFO allocation of payment against supplier invoices

	Args:
		payment_doc: Payments Paid document
		invoices (list): List of invoices to allocate against

	Returns:
		dict: Allocation summary
	"""
	remaining_payment = flt(payment_doc.amount)
	total_allocated = 0
	allocations = []
	failed_allocations = []

	for invoice in invoices:
		if remaining_payment <= 0:
			break

		try:
			invoice_balance = flt(invoice.balance_amount)

			# Validate invoice balance
			if invoice_balance <= 0:
				continue

			# Calculate allocation amount
			allocation_amount = min(remaining_payment, invoice_balance)

			# Validate allocation amount
			if allocation_amount <= 0:
				continue

			# Create allocation record
			allocation = create_supplier_payment_allocation(
				payment_doc.name,
				invoice.name,
				allocation_amount,
				payment_doc.supplier,
				payment_doc.project
			)

			if allocation:
				# Update invoice balance
				new_balance = invoice_balance - allocation_amount
				update_supplier_invoice_balance(invoice.name, new_balance)

				# Update remaining payment
				remaining_payment -= allocation_amount
				total_allocated += allocation_amount

				allocations.append({
					"allocation_name": allocation.name,
					"invoice_name": invoice.name,
					"allocated_amount": allocation_amount,
					"invoice_balance_before": invoice_balance,
					"invoice_balance_after": new_balance
				})

				frappe.logger().info(
					f"Allocated {allocation_amount} from payment {payment_doc.name} "
					f"to invoice {invoice.name}. New balance: {new_balance}"
				)

		except Exception as e:
			failed_allocations.append({
				"invoice": invoice.name,
				"error": str(e)
			})
			frappe.log_error(f"Error allocating to invoice {invoice.name}: {str(e)}", "Supplier Payment Allocation Error")
			continue

	# Update payment unallocated amount
	unallocated_amount = flt(payment_doc.amount) - total_allocated

	# Validate unallocated amount
	if unallocated_amount < 0:
		frappe.log_error(
			f"Negative unallocated amount detected for payment {payment_doc.name}: {unallocated_amount}",
			"Supplier Payment Allocation Error"
		)
		unallocated_amount = 0

	frappe.db.set_value("Payments Paid", payment_doc.name, "unallocated_amount", unallocated_amount)

	result = {
		"allocated_amount": total_allocated,
		"unallocated_amount": unallocated_amount,
		"allocations": allocations
	}

	if failed_allocations:
		result["failed_allocations"] = failed_allocations

	return result


def create_supplier_payment_allocation(payment_name, invoice_name, amount, supplier, project):
	"""
	Create a Supplier Payment Allocation record for audit trail
	
	Args:
		payment_name (str): Payments Paid name
		invoice_name (str): Supplier Invoices name
		amount (float): Allocation amount
		supplier (str): Supplier name
		project (str): Project name
		
	Returns:
		Document: Supplier Payment Allocation document
	"""
	try:
		allocation = frappe.get_doc({
			"doctype": "Supplier Payment Allocation",
			"payments_paid": payment_name,
			"supplier_invoice": invoice_name,
			"supplier": supplier,
			"project": project,
			"allocated_amount": amount,
			"allocation_date": now(),
			"allocation_method": "FIFO",
			"remarks": f"Automatic allocation using FIFO method"
		})
		
		allocation.insert(ignore_permissions=True)
		return allocation
		
	except Exception as e:
		frappe.log_error(f"Error creating supplier payment allocation: {str(e)}", "Supplier Payment Allocation Error")
		return None


def update_supplier_invoice_balance(invoice_name, new_balance):
	"""
	Update the balance amount of a Supplier Invoice with validation and status update

	Args:
		invoice_name (str): Supplier Invoices name
		new_balance (float): New balance amount
	"""
	try:
		# Validate input
		if not invoice_name:
			raise ValueError("Invoice name is required")

		# Ensure balance doesn't go negative
		new_balance = max(0, flt(new_balance))

		# Check if invoice exists
		if not frappe.db.exists("Supplier Invoices", invoice_name):
			raise ValueError(f"Invoice {invoice_name} does not exist")

		# Load the invoice document
		invoice_doc = frappe.get_doc("Supplier Invoices", invoice_name)
		old_balance = flt(invoice_doc.balance_amount)

		# Update balance amount
		invoice_doc.balance_amount = new_balance

		# Update status based on balance amount
		if new_balance <= 0:
			invoice_doc.status = "Paid"
		elif new_balance < flt(invoice_doc.grand_total):
			invoice_doc.status = "Partially Paid"
		else:
			invoice_doc.status = "Not Paid"

		# Save the document to trigger all events and validations
		invoice_doc.save(ignore_permissions=True)

		# Notify clients to refresh the invoice form if it's open
		frappe.publish_realtime(
			"supplier_invoice_balance_updated",
			{"invoice_name": invoice_name, "new_balance": new_balance, "status": invoice_doc.status},
			user=frappe.session.user
		)

		# Log the update for audit
		frappe.logger().info(
			f"Updated balance for supplier invoice {invoice_name}: {old_balance} -> {new_balance}, Status: {invoice_doc.status}"
		)

	except Exception as e:
		frappe.log_error(f"Error updating supplier invoice balance: {str(e)}", "Supplier Payment Allocation Error")
		raise


def validate_allocation_data(payment_doc):
	"""
	Validate payment allocation data

	Args:
		payment_doc: Payments Paid document
	"""
	if not payment_doc.supplier:
		frappe.throw(_("Supplier is required for payment allocation"))

	if not payment_doc.project:
		frappe.throw(_("Project is required for payment allocation"))

	if not payment_doc.amount or flt(payment_doc.amount) <= 0:
		frappe.throw(_("Payment amount must be greater than zero"))


def clear_supplier_payment_allocations(payment_name):
	"""
	Clear all allocations for a payment and restore invoice balances

	Args:
		payment_name (str): Payments Paid name
	"""
	try:
		# Get all allocations for this payment
		allocations = frappe.get_all(
			"Supplier Payment Allocation",
			filters={"payments_paid": payment_name},
			fields=["name", "supplier_invoice", "allocated_amount"]
		)

		if not allocations:
			return

		# Start transaction
		frappe.db.begin()

		try:
			for allocation in allocations:
				# Restore invoice balance
				invoice_doc = frappe.get_doc("Supplier Invoices", allocation.supplier_invoice)
				new_balance = flt(invoice_doc.balance_amount) + flt(allocation.allocated_amount)

				# Update balance and status
				update_supplier_invoice_balance(allocation.supplier_invoice, new_balance)

				# Delete allocation record
				frappe.delete_doc("Supplier Payment Allocation", allocation.name, ignore_permissions=True)

			# Commit transaction
			frappe.db.commit()

			frappe.logger().info(f"Cleared {len(allocations)} allocations for payment {payment_name}")

		except Exception as e:
			frappe.db.rollback()
			raise

	except Exception as e:
		frappe.log_error(f"Error clearing supplier payment allocations: {str(e)}", "Supplier Payment Allocation Error")
		raise


def handle_supplier_payment_allocation_on_save(doc, method):
	"""
	Hook handler for Payments Paid on_save event

	Args:
		doc: Payments Paid document
		method: Event method name
	"""
	# The allocation logic is handled in the document's on_save method
	# This hook is kept for future extensibility
	pass


def handle_supplier_payment_allocation_on_delete(doc, method):
	"""
	Hook handler for Payments Paid on_trash event
	Clears allocations and restores invoice balances

	Args:
		doc: Payments Paid document
		method: Event method name
	"""
	try:
		# Get all allocations for this payment
		allocations = frappe.get_all(
			"Supplier Payment Allocation",
			filters={"payments_paid": doc.name},
			fields=["name", "supplier_invoice", "allocated_amount"]
		)

		if not allocations:
			# No allocations to process
			return

		frappe.logger().info(f"Processing deletion of payment {doc.name} with {len(allocations)} allocations")

		# Clear allocations and restore balances
		clear_supplier_payment_allocations(doc.name)

		frappe.logger().info(f"Successfully processed deletion of payment {doc.name}")

	except Exception as e:
		frappe.log_error(f"Error handling supplier payment deletion: {str(e)}", "Supplier Payment Allocation Error")
		# Don't raise the error to prevent blocking the deletion
		frappe.logger().error(f"Failed to clear allocations for payment {doc.name}: {str(e)}")


@frappe.whitelist()
def manual_reallocate_supplier_payment(payment_name):
	"""
	Manually trigger reallocation of a supplier payment

	Args:
		payment_name (str): Payments Paid name

	Returns:
		dict: Allocation result
	"""
	payment_doc = frappe.get_doc("Payments Paid", payment_name)

	# Clear existing allocations
	clear_supplier_payment_allocations(payment_name)

	# Perform new allocation
	result = allocate_payment_to_supplier_invoices(payment_doc)

	frappe.db.commit()
	return result


def handle_supplier_payment_allocation_record_delete(doc, method):
	"""Handle individual Supplier Payment Allocation record deletion via hooks"""
	try:
		# Restore invoice balance
		if doc.supplier_invoice and doc.allocated_amount:
			current_balance = frappe.db.get_value("Supplier Invoices", doc.supplier_invoice, "balance_amount")
			new_balance = flt(current_balance) + flt(doc.allocated_amount)
			frappe.db.set_value("Supplier Invoices", doc.supplier_invoice, "balance_amount", new_balance)

			# Update invoice status
			invoice_data = frappe.db.get_value("Supplier Invoices", doc.supplier_invoice, ["grand_total"], as_dict=True)
			if invoice_data:
				grand_total = flt(invoice_data.grand_total)
				if new_balance <= 0:
					status = "Paid"
				elif new_balance >= grand_total:
					status = "Not Paid"
				else:
					status = "Partially Paid"

				frappe.db.set_value("Supplier Invoices", doc.supplier_invoice, "status", status)

			frappe.logger().info(f"Restored balance for invoice {doc.supplier_invoice}: +{doc.allocated_amount} = {new_balance}")

	except Exception as e:
		frappe.log_error(f"Error handling supplier payment allocation deletion: {str(e)}", "Supplier Payment Allocation Error")
		# Don't raise the error to prevent blocking the deletion
		frappe.logger().error(f"Failed to restore balance for allocation {doc.name}: {str(e)}")
