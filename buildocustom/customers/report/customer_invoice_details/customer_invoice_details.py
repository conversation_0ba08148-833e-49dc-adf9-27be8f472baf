# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.utils import flt


def execute(filters=None):
	"""Execute Customer Invoice Details report"""
	columns = get_columns()
	data = get_data(filters)

	# Add total row
	if data:
		total_row = calculate_totals(data)
		data.append(total_row)

	# Return with chart data for summary
	summary = []
	if data and len(data) > 1:  # More than just total row
		total_row = data[-1]  # Last row is total
		summary = [
			{
				"value": total_row.get("net_payable", 0),
				"label": "Total Net Payable",
				"datatype": "Currency"
			},
			{
				"value": total_row.get("sub_total", 0),
				"label": "Total Sub Total",
				"datatype": "Currency"
			}
		]

	return columns, data, None, None, summary


def get_columns():
	"""Define report columns"""
	return [
		{
			"fieldname": "date",
			"label": "Date",
			"fieldtype": "Date",
			"width": 100
		},
		{
			"fieldname": "subject",
			"label": "Subject",
			"fieldtype": "Data",
			"width": 200
		},
		{
			"fieldname": "invoice_no",
			"label": "Invoice No",
			"fieldtype": "Data",
			"width": 120
		},
		{
			"fieldname": "sub_total",
			"label": "Sub Total",
			"fieldtype": "Currency",
			"width": 120
		},
		{
			"fieldname": "retention_amount",
			"label": "Retention Amount",
			"fieldtype": "Currency",
			"width": 140
		},
		{
			"fieldname": "total",
			"label": "Total",
			"fieldtype": "Currency",
			"width": 120
		},
		{
			"fieldname": "cgst_amount_with_percent",
			"label": "CGST Amount",
			"fieldtype": "Data",
			"width": 140
		},
		{
			"fieldname": "sgst_amount_with_percent",
			"label": "SGST Amount",
			"fieldtype": "Data",
			"width": 140
		},
		{
			"fieldname": "igst_amount_with_percent",
			"label": "IGST Amount",
			"fieldtype": "Data",
			"width": 140
		},
		{
			"fieldname": "net_payable",
			"label": "Net Payable",
			"fieldtype": "Currency",
			"width": 120
		},
		{
			"fieldname": "tds_amount",
			"label": "TDS Amount",
			"fieldtype": "Currency",
			"width": 120
		}
	]


def get_data(filters):
	"""Fetch and process invoice data"""
	conditions = []
	values = []

	# Build filter conditions
	if filters.get("project"):
		conditions.append("project = %s")
		values.append(filters.get("project"))

	if filters.get("customer"):
		conditions.append("customer = %s")
		values.append(filters.get("customer"))

	if filters.get("from_date"):
		conditions.append("date >= %s")
		values.append(filters.get("from_date"))

	if filters.get("to_date"):
		conditions.append("date <= %s")
		values.append(filters.get("to_date"))

	where_clause = ""
	if conditions:
		where_clause = "WHERE " + " AND ".join(conditions)

	# Query to fetch invoice data
	query = f"""
		SELECT
			date,
			subject,
			invoice_no,
			sub_total,
			retention_amount,
			total,
			cgst_amount,
			cgst_,
			sgst_amount,
			sgst_,
			igst_amount,
			igst_,
			net_payable,
			tds_amount
		FROM `tabClient Invoice`
		{where_clause}
		ORDER BY date DESC
	"""

	invoices = frappe.db.sql(query, values, as_dict=True)

	# Process data to format GST amounts with percentages
	for invoice in invoices:
		# Format CGST amount with percentage
		cgst_percent = flt(invoice.get("cgst_", 0))
		cgst_amount = flt(invoice.get("cgst_amount", 0))
		invoice["cgst_amount_with_percent"] = f"₹{cgst_amount:,.2f} ({cgst_percent}%)" if cgst_amount > 0 else "₹0.00 (0%)"

		# Format SGST amount with percentage
		sgst_percent = flt(invoice.get("sgst_", 0))
		sgst_amount = flt(invoice.get("sgst_amount", 0))
		invoice["sgst_amount_with_percent"] = f"₹{sgst_amount:,.2f} ({sgst_percent}%)" if sgst_amount > 0 else "₹0.00 (0%)"

		# Format IGST amount with percentage
		igst_percent = flt(invoice.get("igst_", 0))
		igst_amount = flt(invoice.get("igst_amount", 0))
		invoice["igst_amount_with_percent"] = f"₹{igst_amount:,.2f} ({igst_percent}%)" if igst_amount > 0 else "₹0.00 (0%)"

	return invoices


def calculate_totals(data):
	"""Calculate totals for all numeric columns"""
	total_sub_total = 0
	total_retention_amount = 0
	total_total = 0
	total_cgst_amount = 0
	total_sgst_amount = 0
	total_igst_amount = 0
	total_net_payable = 0
	total_tds_amount = 0

	for row in data:
		total_sub_total += flt(row.get("sub_total", 0))
		total_retention_amount += flt(row.get("retention_amount", 0))
		total_total += flt(row.get("total", 0))
		total_cgst_amount += flt(row.get("cgst_amount", 0))
		total_sgst_amount += flt(row.get("sgst_amount", 0))
		total_igst_amount += flt(row.get("igst_amount", 0))
		total_net_payable += flt(row.get("net_payable", 0))
		total_tds_amount += flt(row.get("tds_amount", 0))

	# Create total row - use numeric values for Currency fields
	total_row = {
		"date": "",
		"subject": "<b>TOTAL</b>",
		"invoice_no": "",
		"sub_total": total_sub_total,
		"retention_amount": total_retention_amount,
		"total": total_total,
		"cgst_amount_with_percent": f"<b>₹{total_cgst_amount:,.2f}</b>",
		"sgst_amount_with_percent": f"<b>₹{total_sgst_amount:,.2f}</b>",
		"igst_amount_with_percent": f"<b>₹{total_igst_amount:,.2f}</b>",
		"net_payable": total_net_payable,
		"tds_amount": total_tds_amount,
		"_style": "font-weight: bold; background-color: #f0f0f0;"
	}

	return total_row
