# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.utils import flt, getdate, formatdate
from frappe import _


def execute(filters=None):
	"""Main execution function for Customer Ledger Side by Side report"""
	if not filters:
		filters = {}

	try:
		# Get data for both tables
		client_invoices = get_client_invoices(filters)
		payment_received = get_payment_received(filters)

		# Calculate summary metrics
		summary_data = calculate_summary_metrics(client_invoices, payment_received)

		# Create HTML report with side-by-side tables
		html_content = create_side_by_side_html_report(client_invoices, payment_received, summary_data)

		# Return single column with HTML content - increased width for horizontal scroll
		columns = [{"fieldname": "html_content", "label": "", "fieldtype": "HTML", "width": 1600}]
		data = [{"html_content": html_content}]

		return columns, data
	except Exception as e:
		# Return error message
		columns = [{"fieldname": "html_content", "label": "", "fieldtype": "HTML", "width": 1600}]
		data = [{"html_content": f"<div style='color: red; padding: 20px;'>Error: {str(e)}</div>"}]
		return columns, data





def get_client_invoices(filters):
	"""Fetch Client Invoice data with applied filters and child table descriptions"""
	conditions = []
	values = []

	if filters.get("project"):
		conditions.append("ci.project = %s")
		values.append(filters.get("project"))

	if filters.get("customer"):
		conditions.append("ci.customer = %s")
		values.append(filters.get("customer"))

	if filters.get("from_date"):
		conditions.append("ci.date >= %s")
		values.append(filters.get("from_date"))

	if filters.get("to_date"):
		conditions.append("ci.date <= %s")
		values.append(filters.get("to_date"))

	where_clause = ""
	if conditions:
		where_clause = "WHERE " + " AND ".join(conditions)

	# First get the main invoice data
	main_query = f"""
		SELECT
			ci.name,
			ci.date,
			ci.subject,
			ci.invoice_no,
			ci.net_payable,
			ci.tds_amount,
			ci.customer,
			ci.project
		FROM `tabClient Invoice` ci
		{where_clause}
		ORDER BY ci.date DESC
	"""

	invoices = frappe.db.sql(main_query, values, as_dict=True)

	# For each invoice, get descriptions from child tables
	for invoice in invoices:
		descriptions = []

		try:
			if not invoice.get('name'):
				invoice['description'] = ""
				continue

			# Get descriptions from Builtup Invoice Items using frappe.db.get_all
			try:
				builtup_items = frappe.db.get_all(
					"Builtup Invoice Items",
					filters={
						"parent": invoice.name,
						"curr_bill_amt": [">", 0]
					},
					fields=["desc", "curr_bill_amt"]
				)

				for item in builtup_items:
					if item.get('desc'):
						descriptions.append(str(item.get('desc')))
			except:
				pass  # Ignore errors for now

			# Get descriptions from Item Rate Invoice Items using frappe.db.get_all
			try:
				item_rate_items = frappe.db.get_all(
					"Item Rate Invoice Items",
					filters={
						"parent": invoice.name,
						"curr_bill_amt": [">", 0]
					},
					fields=["desc", "curr_bill_amt"]
				)

				for item in item_rate_items:
					if item.get('desc'):
						descriptions.append(str(item.get('desc')))
			except:
				pass  # Ignore errors for now

		except:
			pass  # Ignore all errors to prevent report breaking

		# Set description
		invoice['description'] = ', '.join(descriptions) if descriptions else ""

	return invoices


def get_payment_received(filters):
	"""Fetch Payment Received data with applied filters"""
	conditions = []
	values = []

	if filters.get("project"):
		conditions.append("project = %s")
		values.append(filters.get("project"))

	if filters.get("customer"):
		conditions.append("customer = %s")
		values.append(filters.get("customer"))

	if filters.get("from_date"):
		conditions.append("date >= %s")
		values.append(filters.get("from_date"))

	if filters.get("to_date"):
		conditions.append("date <= %s")
		values.append(filters.get("to_date"))

	where_clause = ""
	if conditions:
		where_clause = "WHERE " + " AND ".join(conditions)

	query = f"""
		SELECT
			date,
			amount,
			payment_mode,
			remarks,
			receipt_no,
			customer,
			project
		FROM `tabPayment Received`
		{where_clause}
		ORDER BY date DESC
	"""

	return frappe.db.sql(query, values, as_dict=True)


def calculate_summary_metrics(client_invoices, payment_received):
	"""Calculate summary metrics for the cards"""
	total_net_payable = sum(flt(invoice.get("net_payable", 0)) for invoice in client_invoices)
	total_tds_amount = sum(flt(invoice.get("tds_amount", 0)) for invoice in client_invoices)
	total_payment_received = sum(flt(payment.get("amount", 0)) for payment in payment_received)
	total_balance = total_net_payable - total_tds_amount - total_payment_received

	return {
		"total_net_payable": total_net_payable,
		"total_tds_amount": total_tds_amount,
		"total_payment_received": total_payment_received,
		"total_balance": total_balance
	}





def create_summary_cards_html(summary_data):
	"""Create summary cards HTML with light grey background and black text"""

	cards_html = f"""
	<div style='display: flex; justify-content: space-around; margin-bottom: 25px; flex-wrap: wrap; width: 100%; padding: 15px; gap: 15px;'>
		<!-- Total Net Payable Card -->
		<div style='background: #f8f9fa; padding: 22px; border-radius: 12px; text-align: center; min-width: 240px; flex: 1; max-width: 280px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); border: 1px solid #e9ecef;'>
			<div style='font-weight: 600; color: #333; margin-bottom: 12px; font-size: 13px; text-transform: uppercase; letter-spacing: 1px;'>💰 Total Net Payable</div>
			<div style='font-size: 24px; color: #000; font-weight: 700;'>{frappe.format(summary_data.get('total_net_payable', 0), {'fieldtype': 'Currency'})}</div>
		</div>

		<!-- Total TDS Amount Card -->
		<div style='background: #f8f9fa; padding: 22px; border-radius: 12px; text-align: center; min-width: 240px; flex: 1; max-width: 280px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); border: 1px solid #e9ecef;'>
			<div style='font-weight: 600; color: #333; margin-bottom: 12px; font-size: 13px; text-transform: uppercase; letter-spacing: 1px;'>📊 Total TDS Amount</div>
			<div style='font-size: 24px; color: #000; font-weight: 700;'>{frappe.format(summary_data.get('total_tds_amount', 0), {'fieldtype': 'Currency'})}</div>
		</div>

		<!-- Total Payment Received Card -->
		<div style='background: #f8f9fa; padding: 22px; border-radius: 12px; text-align: center; min-width: 240px; flex: 1; max-width: 280px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); border: 1px solid #e9ecef;'>
			<div style='font-weight: 600; color: #333; margin-bottom: 12px; font-size: 13px; text-transform: uppercase; letter-spacing: 1px;'>✅ Payment Received</div>
			<div style='font-size: 24px; color: #000; font-weight: 700;'>{frappe.format(summary_data.get('total_payment_received', 0), {'fieldtype': 'Currency'})}</div>
		</div>

		<!-- Outstanding Balance Card -->
		<div style='background: #f8f9fa; padding: 22px; border-radius: 12px; text-align: center; min-width: 240px; flex: 1; max-width: 280px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); border: 1px solid #e9ecef;'>
			<div style='font-weight: 600; color: #333; margin-bottom: 12px; font-size: 13px; text-transform: uppercase; letter-spacing: 1px;'>{"⚠️ Outstanding Balance" if summary_data.get("total_balance", 0) > 0 else "✨ Balance Status"}</div>
			<div style='font-size: 24px; color: #000; font-weight: 700;'>{frappe.format(summary_data.get('total_balance', 0), {'fieldtype': 'Currency'})}</div>
		</div>
	</div>
	"""
	return cards_html


def create_side_by_side_html_report(client_invoices, payment_received, summary_data):
	"""Create a complete HTML report with summary cards and side-by-side tables"""

	# Create summary cards
	summary_cards = create_summary_cards_html(summary_data)

	# Create client invoices table
	invoices_table = create_invoices_table_html(client_invoices)

	# Create payment received table
	payments_table = create_payments_table_html(payment_received)

	# Add debug info if no data found
	debug_info = ""
	if not client_invoices and not payment_received:
		# Get total counts to help with debugging
		total_invoices = frappe.db.count("Client Invoice")
		total_payments = frappe.db.count("Payment Received")

		debug_info = f"""
		<div style='background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 20px 0; color: #856404;'>
			<strong>ℹ️ No Data Found</strong><br>
			No records match the selected filters.
			<br><br>
			<strong>Database Info:</strong>
			<ul style='margin: 10px 0; padding-left: 20px;'>
				<li>Total Client Invoices in system: {total_invoices}</li>
				<li>Total Payment Received in system: {total_payments}</li>
			</ul>
			<strong>Please check:</strong>
			<ul style='margin: 10px 0; padding-left: 20px;'>
				<li>Project and Customer combination exists in the data</li>
				<li>Date range includes existing records</li>
				<li>Field values match exactly (case-sensitive)</li>
			</ul>
		</div>
		"""

	# Combine everything into a complete report with horizontal scrolling
	html_report = f"""
	<div style='width: 100%; overflow-x: auto; padding: 20px; font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; min-width: 1400px;'>
		<!-- Summary Cards -->
		{summary_cards}

		{debug_info}

		<!-- Side by Side Tables Container -->
		<div style='display: flex; gap: 20px; margin-top: 20px; min-width: 1200px;'>
			<!-- Client Invoices Table (Left) -->
			<div style='flex: 1; min-width: 600px;'>
				<h3 style='color: #1976d2; margin-bottom: 15px; font-size: 18px; font-weight: 600; border-bottom: 2px solid #1976d2; padding-bottom: 8px;'>
					📋 Client Invoices ({len(client_invoices)} records)
				</h3>
				{invoices_table}
			</div>

			<!-- Payment Received Table (Right) -->
			<div style='flex: 1; min-width: 600px;'>
				<h3 style='color: #388e3c; margin-bottom: 15px; font-size: 18px; font-weight: 600; border-bottom: 2px solid #388e3c; padding-bottom: 8px;'>
					💰 Payment Received ({len(payment_received)} records)
				</h3>
				{payments_table}
			</div>
		</div>
	</div>
	"""

	return html_report


def create_invoices_table_html(client_invoices):
	"""Create HTML table for client invoices"""
	if not client_invoices:
		return """
		<div style='text-align: center; padding: 40px; color: #666; background: #f8f9fa; border-radius: 8px; border: 1px solid #dee2e6;'>
			<div style='font-size: 16px; margin-bottom: 8px;'>📋</div>
			<div style='font-weight: 500; margin-bottom: 5px;'>No client invoices found</div>
			<div style='font-size: 12px; color: #999;'>Try adjusting your filters</div>
		</div>
		"""

	table_html = """
	<div style='max-height: 400px; overflow-y: auto; overflow-x: auto; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border: 1px solid #e0e6ed;'>
		<table style='width: 100%; border-collapse: collapse; background: white; font-size: 13px;'>
			<thead style='position: sticky; top: 0; z-index: 10;'>
				<tr style='background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);'>
					<th style='padding: 12px 8px; text-align: left; font-weight: 600; color: #1976d2; border-bottom: 2px solid #1976d2; font-size: 12px;'>Date</th>
					<th style='padding: 12px 8px; text-align: left; font-weight: 600; color: #1976d2; border-bottom: 2px solid #1976d2; font-size: 12px;'>Subject</th>
					<th style='padding: 12px 8px; text-align: left; font-weight: 600; color: #1976d2; border-bottom: 2px solid #1976d2; font-size: 12px;'>Invoice No</th>
					<th style='padding: 12px 8px; text-align: right; font-weight: 600; color: #1976d2; border-bottom: 2px solid #1976d2; font-size: 12px;'>Net Payable</th>
					<th style='padding: 12px 8px; text-align: right; font-weight: 600; color: #1976d2; border-bottom: 2px solid #1976d2; font-size: 12px;'>TDS Amount</th>
					<th style='padding: 12px 8px; text-align: left; font-weight: 600; color: #1976d2; border-bottom: 2px solid #1976d2; font-size: 12px;'>Description</th>
				</tr>
			</thead>
			<tbody>
	"""

	for i, invoice in enumerate(client_invoices):
		row_bg = "#f8f9ff" if i % 2 == 0 else "white"

		# Get description from child tables (already filtered for curr_bill_amt > 0)
		description = str(invoice.get("description", "") or "")
		display_description = description[:100] if description else ""
		if len(description) > 97:
			display_description = description[:97] + "..."

		table_html += f"""
		<tr style='background: {row_bg}; border-bottom: 1px solid #e0e6ed;'>
			<td style='padding: 10px 8px; border-right: 1px solid #e0e6ed;'>{formatdate(invoice.get("date")) if invoice.get("date") else ""}</td>
			<td style='padding: 10px 8px; border-right: 1px solid #e0e6ed;'>{invoice.get("subject", "")}</td>
			<td style='padding: 10px 8px; border-right: 1px solid #e0e6ed;'>{invoice.get("invoice_no", "")}</td>
			<td style='padding: 10px 8px; text-align: right; font-weight: 500; border-right: 1px solid #e0e6ed;'>{frappe.format(flt(invoice.get("net_payable", 0)), {"fieldtype": "Currency"}) if invoice.get("net_payable") else ""}</td>
			<td style='padding: 10px 8px; text-align: right; font-weight: 500; border-right: 1px solid #e0e6ed;'>{frappe.format(flt(invoice.get("tds_amount", 0)), {"fieldtype": "Currency"}) if invoice.get("tds_amount") else ""}</td>
			<td style='padding: 10px 8px;' title='{description}'>{display_description}</td>
		</tr>
		"""

	table_html += """
			</tbody>
		</table>
	</div>
	"""

	return table_html


def create_payments_table_html(payment_received):
	"""Create HTML table for payment received"""
	if not payment_received:
		return """
		<div style='text-align: center; padding: 40px; color: #666; background: #f8f9fa; border-radius: 8px; border: 1px solid #dee2e6;'>
			<div style='font-size: 16px; margin-bottom: 8px;'>💰</div>
			<div style='font-weight: 500; margin-bottom: 5px;'>No payments found</div>
			<div style='font-size: 12px; color: #999;'>Try adjusting your filters</div>
		</div>
		"""

	table_html = """
	<div style='max-height: 400px; overflow-y: auto; overflow-x: auto; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border: 1px solid #e0e6ed;'>
		<table style='width: 100%; border-collapse: collapse; background: white; font-size: 13px;'>
			<thead style='position: sticky; top: 0; z-index: 10;'>
				<tr style='background: linear-gradient(135deg, #e8f5e8 0%, #a5d6a7 100%);'>
					<th style='padding: 12px 8px; text-align: left; font-weight: 600; color: #388e3c; border-bottom: 2px solid #388e3c; font-size: 12px;'>Date</th>
					<th style='padding: 12px 8px; text-align: right; font-weight: 600; color: #388e3c; border-bottom: 2px solid #388e3c; font-size: 12px;'>Amount</th>
					<th style='padding: 12px 8px; text-align: left; font-weight: 600; color: #388e3c; border-bottom: 2px solid #388e3c; font-size: 12px;'>Payment Mode</th>
					<th style='padding: 12px 8px; text-align: left; font-weight: 600; color: #388e3c; border-bottom: 2px solid #388e3c; font-size: 12px;'>Receipt No</th>
					<th style='padding: 12px 8px; text-align: left; font-weight: 600; color: #388e3c; border-bottom: 2px solid #388e3c; font-size: 12px;'>Remarks</th>
				</tr>
			</thead>
			<tbody>
	"""

	for i, payment in enumerate(payment_received):
		row_bg = "#f0fff4" if i % 2 == 0 else "white"
		remarks = str(payment.get("remarks", "") or "")[:50]
		if len(remarks) > 47:
			remarks = remarks[:47] + "..."

		table_html += f"""
		<tr style='background: {row_bg}; border-bottom: 1px solid #e0e6ed;'>
			<td style='padding: 10px 8px; border-right: 1px solid #e0e6ed;'>{formatdate(payment.get("date")) if payment.get("date") else ""}</td>
			<td style='padding: 10px 8px; text-align: right; font-weight: 500; border-right: 1px solid #e0e6ed;'>{frappe.format(flt(payment.get("amount", 0)), {"fieldtype": "Currency"}) if payment.get("amount") else ""}</td>
			<td style='padding: 10px 8px; border-right: 1px solid #e0e6ed;'>{payment.get("payment_mode", "")}</td>
			<td style='padding: 10px 8px; border-right: 1px solid #e0e6ed;'>{payment.get("receipt_no", "")}</td>
			<td style='padding: 10px 8px;' title='{payment.get("remarks", "")}'>{remarks}</td>
		</tr>
		"""

	table_html += """
			</tbody>
		</table>
	</div>
	"""

	return table_html
