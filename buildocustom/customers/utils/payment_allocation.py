# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.utils import flt


def handle_payment_allocation_on_save(doc, method):
	"""
	Hook handler for Payment Received on_save event

	Args:
		doc: Payment Received document
		method: Event method name
	"""
	# The allocation logic is handled in the document's after_insert and on_update methods
	# This hook is kept for future extensibility
	pass


def handle_payment_allocation_on_delete(doc, method):
	"""
	Hook handler for Payment Received on_trash event

	Args:
		doc: Payment Received document
		method: Event method name
	"""
	try:
		# Clear existing allocations and restore invoice balances
		allocations = frappe.get_all(
			"Payment Allocation",
			filters={"payment_received": doc.name},
			fields=["name", "client_invoice", "allocated_amount"]
		)

		for allocation in allocations:
			# Restore invoice balance
			invoice = frappe.get_doc("Client Invoice", allocation.client_invoice)
			new_balance = flt(invoice.balance_amount) + flt(allocation.allocated_amount)
			invoice.balance_amount = new_balance

			# Update invoice status
			if new_balance <= 0:
				status = "Fully Paid"
			elif new_balance >= (invoice.net_payable - invoice.tds_amount):
				status = "Not Paid"
			else:
				status = "Partially Paid"

			invoice.status = status
			invoice.save(ignore_permissions=True)

			# Delete allocation record
			frappe.delete_doc("Payment Allocation", allocation.name, ignore_permissions=True)

	except Exception as e:
		frappe.log_error(f"Error handling payment deletion: {str(e)}", "Payment Allocation Error")
		# Don't raise the error to prevent blocking the deletion
		frappe.logger().error(f"Failed to clear allocations for payment {doc.name}: {str(e)}")


@frappe.whitelist()
def manual_reallocate_payment(payment_name):
	"""Manually reallocate a payment"""
	try:
		payment_doc = frappe.get_doc("Payment Received", payment_name)
		payment_doc.allocate_payment()

		return {
			"success": True,
			"message": "Payment reallocated successfully",
			"unallocated_amount": payment_doc.unallocated_amount
		}
	except Exception as e:
		frappe.log_error(f"Error in manual reallocation: {str(e)}", "Manual Reallocation Error")
		return {
			"success": False,
			"message": str(e)
		}


@frappe.whitelist()
def get_outstanding_invoices(customer, project):
	"""Get outstanding invoices for a customer and project"""
	try:
		invoices = frappe.get_all(
			"Client Invoice",
			filters={
				"customer": customer,
				"project": project,
				"balance_amount": [">", 0],
				"docstatus": 1
			},
			fields=["name as invoice_no", "date", "net_payable", "tds_amount", "balance_amount", "status"],
			order_by="date asc"
		)

		return invoices

	except Exception as e:
		frappe.log_error(f"Error fetching outstanding invoices: {str(e)}", "Payment Allocation Error")
		return []


@frappe.whitelist()
def get_outstanding_invoices_v2(customer, project):
	"""Get outstanding invoices for a customer and project"""
	return frappe.get_all(
		"Client Invoice",
		filters={
			"customer": customer,
			"project": project,
			"balance_amount": [">", 0],
			"docstatus": 1
		},
		fields=[
			"name", "date", "invoice_no", "net_payable", 
			"tds_amount", "balance_amount", "status"
		],
		order_by="date asc"
	)


@frappe.whitelist()
def get_payment_allocations(payment_name):
	"""Get allocation details for a payment"""
	allocations = frappe.get_all(
		"Payment Allocation",
		filters={"payment_received": payment_name},
		fields=[
			"name", "client_invoice", "allocated_amount", 
			"allocation_date", "customer", "project"
		],
		order_by="allocation_date asc"
	)
	
	# Get invoice details
	for allocation in allocations:
		invoice = frappe.get_doc("Client Invoice", allocation.client_invoice)
		allocation.update({
			"invoice_no": invoice.invoice_no,
			"invoice_date": invoice.date,
			"invoice_amount": invoice.net_payable,
			"current_balance": invoice.balance_amount
		})
	
	return allocations


@frappe.whitelist()
def validate_payment_data(customer, project, amount):
	"""Validate payment data before saving"""
	errors = []
	
	# Check if customer exists
	if not frappe.db.exists("Customers", customer):
		errors.append(f"Customer '{customer}' does not exist")
	
	# Check if project exists
	if not frappe.db.exists("Projects", project):
		errors.append(f"Project '{project}' does not exist")
	
	# Check amount
	if flt(amount) <= 0:
		errors.append("Amount must be greater than zero")
	
	# Check if there are outstanding invoices
	outstanding_invoices = get_outstanding_invoices(customer, project)
	if not outstanding_invoices:
		errors.append("No outstanding invoices found for this customer and project")
	
	return {
		"valid": len(errors) == 0,
		"errors": errors,
		"outstanding_invoices": outstanding_invoices if len(errors) == 0 else []
	}


def recalculate_invoice_status(invoice_name):
	"""Recalculate and update invoice status"""
	try:
		invoice = frappe.get_doc("Client Invoice", invoice_name)
		balance_amount = flt(invoice.balance_amount)
		net_payable = flt(invoice.net_payable) - flt(invoice.tds_amount)
		
		if balance_amount <= 0:
			status = "Fully Paid"
		elif balance_amount >= net_payable:
			status = "Not Paid"
		else:
			status = "Partially Paid"
		
		if invoice.status != status:
			invoice.status = status
			invoice.save(ignore_permissions=True)
			
		return status
	except Exception as e:
		frappe.log_error(f"Error recalculating invoice status: {str(e)}", "Invoice Status Error")
		return None


@frappe.whitelist()
def get_allocation_summary(customer=None, project=None, from_date=None, to_date=None):
	"""Get allocation summary with filters"""
	filters = {}
	
	if customer:
		filters["customer"] = customer
	if project:
		filters["project"] = project
	if from_date:
		filters["allocation_date"] = [">=", from_date]
	if to_date:
		if "allocation_date" in filters:
			filters["allocation_date"] = ["between", [from_date, to_date]]
		else:
			filters["allocation_date"] = ["<=", to_date]
	
	allocations = frappe.get_all(
		"Payment Allocation",
		filters=filters,
		fields=[
			"payment_received", "client_invoice", "allocated_amount",
			"allocation_date", "customer", "project"
		],
		order_by="allocation_date desc"
	)
	
	# Calculate summary
	total_allocated = sum(flt(alloc.allocated_amount) for alloc in allocations)
	unique_payments = len(set(alloc.payment_received for alloc in allocations))
	unique_invoices = len(set(alloc.client_invoice for alloc in allocations))
	
	return {
		"allocations": allocations,
		"summary": {
			"total_allocated": total_allocated,
			"total_payments": unique_payments,
			"total_invoices": unique_invoices,
			"allocation_count": len(allocations)
		}
	}


@frappe.whitelist()
def delete_payment_allocation(allocation_name):
	"""Delete a specific payment allocation and restore invoice balance"""
	try:
		allocation = frappe.get_doc("Payment Allocation", allocation_name)

		# Restore invoice balance
		invoice = frappe.get_doc("Client Invoice", allocation.client_invoice)
		new_balance = flt(invoice.balance_amount) + flt(allocation.allocated_amount)
		invoice.balance_amount = new_balance
		invoice.save(ignore_permissions=True)

		# Recalculate invoice status
		recalculate_invoice_status(allocation.client_invoice)

		# Update payment unallocated amount
		payment = frappe.get_doc("Payment Received", allocation.payment_received)
		payment.unallocated_amount = flt(payment.unallocated_amount) + flt(allocation.allocated_amount)
		payment.save(ignore_permissions=True)

		# Delete allocation
		allocation.delete()

		return {
			"success": True,
			"message": "Allocation deleted successfully"
		}
	except Exception as e:
		frappe.log_error(f"Error deleting allocation: {str(e)}", "Delete Allocation Error")
		return {
			"success": False,
			"message": str(e)
		}


def handle_payment_allocation_record_delete(doc, method):
	"""Handle individual Payment Allocation record deletion via hooks"""
	try:
		# Restore invoice balance
		if doc.client_invoice and doc.allocated_amount:
			current_balance = frappe.db.get_value("Client Invoice", doc.client_invoice, "balance_amount")
			new_balance = flt(current_balance) + flt(doc.allocated_amount)
			frappe.db.set_value("Client Invoice", doc.client_invoice, "balance_amount", new_balance)

			# Update invoice status
			invoice_data = frappe.db.get_value("Client Invoice", doc.client_invoice,
											 ["net_payable", "tds_amount"], as_dict=True)
			if invoice_data:
				net_payable = flt(invoice_data.net_payable) - flt(invoice_data.tds_amount)

				if new_balance <= 0:
					status = "Fully Paid"
				elif new_balance >= net_payable:
					status = "Not Paid"
				else:
					status = "Partially Paid"

				frappe.db.set_value("Client Invoice", doc.client_invoice, "status", status)

		# Update payment unallocated amount
		if doc.payment_received and doc.allocated_amount:
			current_unallocated = frappe.db.get_value("Payment Received", doc.payment_received, "unallocated_amount")
			new_unallocated = flt(current_unallocated) + flt(doc.allocated_amount)
			frappe.db.set_value("Payment Received", doc.payment_received, "unallocated_amount", new_unallocated)

		frappe.logger().info(f"Restored {doc.allocated_amount} to invoice {doc.client_invoice} and updated payment {doc.payment_received}")

	except Exception as e:
		frappe.log_error(f"Error in Payment Allocation record deletion: {str(e)}", "Payment Allocation Record Deletion Error")
		# Don't raise the error to prevent blocking the deletion
		frappe.logger().error(f"Failed to restore balances for allocation {doc.name}: {str(e)}")
