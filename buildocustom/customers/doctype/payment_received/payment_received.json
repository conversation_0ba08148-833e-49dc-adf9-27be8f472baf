{"actions": [], "allow_rename": 1, "autoname": "field:receipt_no", "creation": "2025-07-28 22:47:00.725407", "doctype": "DocType", "engine": "InnoDB", "field_order": ["receipt_no", "project", "customer", "remarks", "column_break_xkoi", "date", "amount", "unallocated_amount", "payment_mode", "reference_no", "section_break_allocation", "allocation_details"], "fields": [{"fieldname": "receipt_no", "fieldtype": "Data", "label": "Receipt No", "unique": 1}, {"fieldname": "project", "fieldtype": "Link", "label": "Project", "options": "Projects"}, {"fieldname": "customer", "fieldtype": "Link", "label": "Customer", "options": "Customers"}, {"fieldname": "remarks", "fieldtype": "Small Text", "label": "Remarks"}, {"fieldname": "column_break_xkoi", "fieldtype": "Column Break"}, {"fieldname": "date", "fieldtype": "Date", "label": "Date"}, {"fieldname": "amount", "fieldtype": "Float", "label": "Amount", "precision": "2"}, {"fieldname": "unallocated_amount", "fieldtype": "Float", "label": "Unallocated Amount", "precision": "2", "read_only": 1}, {"default": "Bank Transfer", "fieldname": "payment_mode", "fieldtype": "Select", "label": "Payment Mode", "options": "\nBank Transfer\nUPI\nCash\nCheque\nOthers"}, {"fieldname": "reference_no", "fieldtype": "Data", "label": "Reference No"}, {"fieldname": "section_break_allocation", "fieldtype": "Section Break", "label": "Payment Allocation Details"}, {"fieldname": "allocation_details", "fieldtype": "Table", "label": "Allocation Details", "options": "Payment Allocation Detail", "read_only": 1}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-28 22:47:00.725407", "modified_by": "Administrator", "module": "Customers", "name": "Payment Received", "naming_rule": "Expression (old style)", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}