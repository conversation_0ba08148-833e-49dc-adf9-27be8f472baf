// Copyright (c) 2025, <PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.ui.form.on("wo order", {
	refresh(frm) {
		// Trigger calculations on load if initial values exist for table_hnbg
		if (frm.doc.table_hnbg && frm.doc.table_hnbg.length) {
			frm.doc.table_hnbg.forEach(function(row) {
				calculate_wo_order_amount(frm, row.doctype, row.name);
			});
		}
		calculate_total_amount(frm);
	}
});

// Function to calculate amount for WO Order Table items
function calculate_wo_order_amount(frm, cdt, cdn) {
	let row = locals[cdt][cdn];
	let qty_area = parseFloat(row.qty__area) || 0;
	let rate = parseFloat(row.rate) || 0;

	row.amount = qty_area * rate;

	frm.refresh_field(cdn, 'amount', 'table_hnbg');
	calculate_total_amount(frm);
	console.log("Calculated amount for WO Order Table item:", row.amount);
}

// Event handler for the child table (WO Order Table)
frappe.ui.form.on('wo order table', {
	// Event handlers for changes in relevant fields
	qty__area: function(frm, cdt, cdn) {
		calculate_wo_order_amount(frm, cdt, cdn);
	},
	rate: function(frm, cdt, cdn) {
		calculate_wo_order_amount(frm, cdt, cdn);
	},
	
	// Event handler when a row is added to the child table
	table_hnbg_add: function(frm, cdt, cdn) {
		calculate_wo_order_amount(frm, cdt, cdn);
	},

	// Event handler when a row is removed from the child table
	table_hnbg_remove: function(frm, cdt, cdn) {
		calculate_total_amount(frm);
	}
});

// Function to calculate the total amount for the WO Order doctype
function calculate_total_amount(frm) {
    let total_amount = 0;
    if (frm.doc.table_hnbg && frm.doc.table_hnbg.length) {
        frm.doc.table_hnbg.forEach(function(row) {
            total_amount += parseFloat(row.amount) || 0;
        });
    }
    frm.set_value('total', total_amount);
    frm.refresh_field('total');
    console.log("Total amount for WO Order:", total_amount);
}
