{"actions": [], "allow_rename": 1, "autoname": "format:{prefix_for_work_order_no}-{######}", "creation": "2025-07-28 22:38:25.628093", "doctype": "DocType", "engine": "InnoDB", "field_order": ["date", "column_break_fctx", "contractor", "column_break_fkxr", "prefix_for_work_order_no", "section_break_xawg", "subject", "section_break_wkst", "table_hnbg", "total", "section_break_phoc", "terms_conditions"], "fields": [{"fieldname": "date", "fieldtype": "Date", "label": "Date"}, {"fieldname": "column_break_fctx", "fieldtype": "Column Break"}, {"fieldname": "contractor", "fieldtype": "Link", "label": "Contractor", "options": "Contractors"}, {"fieldname": "column_break_fkxr", "fieldtype": "Column Break"}, {"fieldname": "prefix_for_work_order_no", "fieldtype": "Data", "label": "Prefix for Work Order No"}, {"fieldname": "section_break_xawg", "fieldtype": "Section Break"}, {"fieldname": "subject", "fieldtype": "Data", "label": "Subject"}, {"fieldname": "section_break_wkst", "fieldtype": "Section Break"}, {"fieldname": "table_hnbg", "fieldtype": "Table", "options": "wo order table"}, {"fieldname": "total", "fieldtype": "Float", "label": "Total"}, {"fieldname": "section_break_phoc", "fieldtype": "Section Break"}, {"fieldname": "terms_conditions", "fieldtype": "Text Editor", "label": "Terms & Conditions"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-28 22:38:25.628093", "modified_by": "Administrator", "module": "Work Order", "name": "wo order", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}