{"actions": [], "allow_rename": 1, "autoname": "format:PTC-{#####}", "creation": "2025-07-28 22:37:57.823057", "doctype": "DocType", "engine": "InnoDB", "field_order": ["start_date", "column_break_sluy", "project", "column_break_tsfa", "staff", "section_break_gglv", "opening_balance", "column_break_opbl", "total_petty_cash_received", "column_break_xzxv", "total_expense", "column_break_kpzu", "balance_in_hand", "section_break_gopz", "table_zznz", "section_break_boch", "notes"], "fields": [{"fieldname": "start_date", "fieldtype": "Date", "label": "Start Date"}, {"fieldname": "column_break_sluy", "fieldtype": "Column Break"}, {"fieldname": "project", "fieldtype": "Link", "label": "Project", "options": "Projects"}, {"fieldname": "column_break_tsfa", "fieldtype": "Column Break"}, {"fieldname": "staff", "fieldtype": "Link", "label": "Staff", "options": "Staff"}, {"fieldname": "section_break_gglv", "fieldtype": "Section Break"}, {"fieldname": "opening_balance", "fieldtype": "Float", "label": "Opening Balance / Prev Carry Forward"}, {"fieldname": "column_break_opbl", "fieldtype": "Column Break"}, {"fieldname": "total_petty_cash_received", "fieldtype": "Float", "label": "Total Petty Cash Received", "read_only": 1}, {"fieldname": "column_break_xzxv", "fieldtype": "Column Break"}, {"fieldname": "total_expense", "fieldtype": "Float", "label": "Total Expense", "read_only": 1}, {"fieldname": "column_break_kpzu", "fieldtype": "Column Break"}, {"fieldname": "balance_in_hand", "fieldtype": "Float", "label": "Balance In Hand", "read_only": 1}, {"fieldname": "section_break_gopz", "fieldtype": "Section Break"}, {"fieldname": "table_zznz", "fieldtype": "Table", "options": "Petty Cash Table"}, {"fieldname": "section_break_boch", "fieldtype": "Section Break"}, {"fieldname": "notes", "fieldtype": "Small Text", "label": "Notes"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-28 22:37:57.823057", "modified_by": "Administrator", "module": "Petty Cash", "name": "Petty Cash", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}