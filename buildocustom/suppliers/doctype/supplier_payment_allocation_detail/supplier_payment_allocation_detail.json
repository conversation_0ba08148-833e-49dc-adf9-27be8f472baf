{"actions": [], "allow_rename": 1, "creation": "2025-07-28 22:47:01.428485", "doctype": "DocType", "engine": "InnoDB", "field_order": ["supplier_invoice", "invoice_date", "invoice_amount", "column_break_1", "allocated_amount", "invoice_balance_before", "invoice_balance_after"], "fields": [{"fieldname": "supplier_invoice", "fieldtype": "Link", "in_list_view": 1, "label": "Supplier Invoice", "options": "Supplier Invoices", "read_only": 1, "reqd": 1}, {"fieldname": "invoice_date", "fieldtype": "Date", "in_list_view": 1, "label": "Invoice Date", "read_only": 1}, {"fieldname": "invoice_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Invoice Amount", "precision": "2", "read_only": 1}, {"fieldname": "column_break_1", "fieldtype": "Column Break"}, {"fieldname": "allocated_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Allocated Amount", "precision": "2", "read_only": 1, "reqd": 1}, {"fieldname": "invoice_balance_before", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Balance Before", "precision": "2", "read_only": 1}, {"fieldname": "invoice_balance_after", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Balance After", "precision": "2", "read_only": 1}], "grid_page_length": 50, "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2025-07-28 22:47:01.428485", "modified_by": "Administrator", "module": "Suppliers", "name": "Supplier Payment Allocation Detail", "naming_rule": "Random", "owner": "Administrator", "permissions": [], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}