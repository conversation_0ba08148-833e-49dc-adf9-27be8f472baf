{"actions": [], "allow_rename": 1, "autoname": "field:payment_id_no", "creation": "2025-07-28 22:47:27.303597", "doctype": "DocType", "engine": "InnoDB", "field_order": ["payment_id_no", "project", "supplier", "remarks", "column_break_atet", "date", "amount", "unallocated_amount", "payment_mode", "reference_no", "section_break_allocation", "allocation_details"], "fields": [{"fieldname": "payment_id_no", "fieldtype": "Data", "label": "Payment ID No", "unique": 1}, {"fieldname": "project", "fieldtype": "Link", "label": "Project", "options": "Projects"}, {"fieldname": "supplier", "fieldtype": "Link", "label": "Supplier", "options": "Suppliers"}, {"fieldname": "remarks", "fieldtype": "Small Text", "label": "Remarks"}, {"fieldname": "column_break_atet", "fieldtype": "Column Break"}, {"fieldname": "date", "fieldtype": "Date", "label": "Date"}, {"fieldname": "amount", "fieldtype": "Float", "label": "Amount"}, {"fieldname": "unallocated_amount", "fieldtype": "Float", "label": "Unallocated Amount", "read_only": 1}, {"default": "Bank Transfer", "fieldname": "payment_mode", "fieldtype": "Select", "label": "Payment Mode", "options": "\nBank Transfer\nUPI\nCheque\nCash\nOthers"}, {"fieldname": "reference_no", "fieldtype": "Data", "label": "Reference No"}, {"fieldname": "section_break_allocation", "fieldtype": "Section Break", "label": "Payment Allocation Details"}, {"fieldname": "allocation_details", "fieldtype": "Table", "label": "Allocation Details", "options": "Supplier Payment Allocation Detail", "read_only": 1}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-28 22:47:27.303597", "modified_by": "Administrator", "module": "Suppliers", "name": "Payments Paid", "naming_rule": "Expression (old style)", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}