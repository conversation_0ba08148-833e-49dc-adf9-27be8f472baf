{"actions": [], "allow_rename": 1, "autoname": "format:{project}-{supplier}-{supplier_invoice_no}", "creation": "2025-07-28 22:42:48.455277", "doctype": "DocType", "engine": "InnoDB", "field_order": ["project", "supplier", "column_break_eabt", "date", "supplier_invoice_no", "status", "section_break_clyb", "table_wkfs", "section_break_uqaa", "cgst_perc", "sgst_perc", "igst_perc", "tcs_perc", "column_break_wobe", "sub_total", "cgst_amount", "sgst_amount", "igst_amount", "tcs_amount", "grand_total", "balance_amount", "section_break_qphz", "remarks"], "fields": [{"fieldname": "project", "fieldtype": "Link", "label": "Project", "options": "Projects"}, {"fieldname": "supplier", "fieldtype": "Link", "label": "Supplier", "options": "Suppliers"}, {"fieldname": "column_break_eabt", "fieldtype": "Column Break"}, {"fieldname": "date", "fieldtype": "Date", "label": "Date"}, {"fieldname": "supplier_invoice_no", "fieldtype": "Data", "label": "Supplier Invoice No"}, {"default": "Not Paid", "fieldname": "status", "fieldtype": "Select", "label": "Status", "options": "Not Paid\nPartially Paid\nPaid"}, {"fieldname": "section_break_clyb", "fieldtype": "Section Break"}, {"fieldname": "table_wkfs", "fieldtype": "Table", "options": "supplier item table"}, {"fieldname": "section_break_uqaa", "fieldtype": "Section Break"}, {"fieldname": "cgst_perc", "fieldtype": "Float", "label": "Cgst perc"}, {"fieldname": "sgst_perc", "fieldtype": "Float", "label": "Sgst perc"}, {"fieldname": "igst_perc", "fieldtype": "Float", "label": "Igst perc"}, {"fieldname": "tcs_perc", "fieldtype": "Float", "label": "Tcs perc"}, {"fieldname": "column_break_wobe", "fieldtype": "Column Break"}, {"fieldname": "sub_total", "fieldtype": "Float", "label": "Sub Total"}, {"fieldname": "cgst_amount", "fieldtype": "Float", "label": "CGST Amount"}, {"fieldname": "sgst_amount", "fieldtype": "Float", "label": "SGST Amount"}, {"fieldname": "igst_amount", "fieldtype": "Float", "label": "IGST Amount"}, {"fieldname": "tcs_amount", "fieldtype": "Float", "label": "TCS Amount"}, {"fieldname": "grand_total", "fieldtype": "Float", "label": "Grand Total"}, {"fieldname": "balance_amount", "fieldtype": "Float", "label": "Balance Amount"}, {"fieldname": "section_break_qphz", "fieldtype": "Section Break"}, {"fieldname": "remarks", "fieldtype": "Small Text", "label": "Remarks"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-28 22:42:48.455277", "modified_by": "Administrator", "module": "Suppliers", "name": "Supplier Invoices", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}