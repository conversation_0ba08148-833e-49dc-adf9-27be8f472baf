// Copyright (c) 2025, <PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.query_reports["Contractor Ledger Side by Side"] = {
	"filters": [
		{
			"fieldname": "project",
			"label": __("Project"),
			"fieldtype": "Link",
			"options": "Projects",
			"width": "100px"
		},
		{
			"fieldname": "contractor",
			"label": __("Contractor"),
			"fieldtype": "Link",
			"options": "Contractors",
			"width": "100px"
		},
		{
			"fieldname": "from_date",
			"label": __("From Date"),
			"fieldtype": "Date",
			"width": "100px"
		},
		{
			"fieldname": "to_date",
			"label": __("To Date"),
			"fieldtype": "Date",
			"width": "100px"
		}
	],

	"onload": function(report) {
		// Add custom styling for the HTML report
		this.add_custom_styles();

		// Add refresh button
		report.page.add_inner_button(__("Refresh"), function() {
			report.refresh();
		});

		// Add custom export button
		report.page.add_inner_button(__("Export to PDF"), function() {
			frappe.contractor_ledger_side_by_side.export_to_pdf(report);
		});

		// Add custom print button
		report.page.add_inner_button(__("Print"), function() {
			frappe.contractor_ledger_side_by_side.print_report(report);
		});
	},

	"add_custom_styles": function() {
		// Add custom CSS for better HTML rendering
		if (!document.getElementById('contractor-ledger-custom-styles')) {
			const style = document.createElement('style');
			style.id = 'contractor-ledger-custom-styles';
			style.textContent = `
				/* Custom styles for Contractor Ledger Side by Side report */
				.report-wrapper .datatable .dt-row .dt-cell {
					vertical-align: top !important;
					padding: 0 !important;
				}

				.report-wrapper .datatable .dt-cell .dt-cell__content {
					padding: 0 !important;
					overflow: visible !important;
				}

				/* Hide the default datatable header */
				.report-wrapper .datatable .dt-header {
					display: none !important;
				}

				/* Hide column headers */
				.report-wrapper .datatable .dt-scrollable .dt-row--header {
					display: none !important;
				}

				/* Ensure HTML content takes full width */
				.report-wrapper .datatable .dt-cell[data-col-index="0"] {
					width: 100% !important;
					max-width: none !important;
				}

				/* Remove default table styling that might interfere */
				.report-wrapper .datatable .dt-cell {
					border: none !important;
					background: transparent !important;
				}

				/* Enable horizontal scrolling for the report content */
				.report-wrapper .datatable .dt-scrollable {
					overflow-x: auto !important;
					overflow-y: visible !important;
				}

				.report-wrapper .datatable .dt-cell .dt-cell__content {
					overflow: visible !important;
					white-space: nowrap !important;
				}

				/* Ensure the main container allows horizontal scroll */
				.report-wrapper {
					overflow-x: auto !important;
				}
			`;
			document.head.appendChild(style);
		}
	},

	"formatter": function(value, row, column, data, default_formatter) {
		// Handle HTML content - return as-is for proper rendering
		if (column.fieldname === "html_content" && value) {
			return value;
		}

		return default_formatter(value, row, column, data);
	}
};

// Custom export and print functions
frappe.contractor_ledger_side_by_side = {
	export_to_pdf: function(report) {
		// Get the HTML content from the report data
		if (report.data && report.data.length > 0 && report.data[0].html_content) {
			const html_content = report.data[0].html_content;

			// Create a new window for PDF export
			const printWindow = window.open('', '_blank');
			printWindow.document.write(`
				<!DOCTYPE html>
				<html>
					<head>
						<title>Contractor Ledger Side by Side Report</title>
						<style>
							body { font-family: Arial, sans-serif; margin: 20px; }
							.summary-cards { margin-bottom: 20px; }
							table { width: 100%; border-collapse: collapse; }
							th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
							th { background-color: #f2f2f2; }
						</style>
					</head>
					<body>
						<h1>Contractor Ledger Side by Side Report</h1>
						${html_content}
					</body>
				</html>
			`);
			printWindow.document.close();

			// Trigger print dialog
			setTimeout(() => {
				printWindow.print();
			}, 500);
		} else {
			frappe.msgprint(__("No data to export"));
		}
	},

	print_report: function(report) {
		// Get the HTML content from the report data
		if (report.data && report.data.length > 0 && report.data[0].html_content) {
			const html_content = report.data[0].html_content;

			// Create a new window for printing
			const printWindow = window.open('', '_blank');
			printWindow.document.write(`
				<!DOCTYPE html>
				<html>
					<head>
						<title>Contractor Ledger Side by Side Report</title>
						<style>
							body { font-family: Arial, sans-serif; margin: 20px; }
							.summary-cards { margin-bottom: 20px; }
							table { width: 100%; border-collapse: collapse; }
							th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
							th { background-color: #f2f2f2; }
						</style>
					</head>
					<body>
						<h1>Contractor Ledger Side by Side Report</h1>
						${html_content}
						<script>
							window.onload = function() {
								window.print();
							}
						</script>
					</body>
				</html>
			`);
			printWindow.document.close();
		} else {
			frappe.msgprint(__("No data to print"));
		}
	}
};

// Extend the original report configuration
Object.assign(frappe.query_reports["Contractor Ledger Side by Side"], {
	"get_datatable_options": function(options) {
		// Customize datatable options for HTML content
		return Object.assign(options, {
			checkboxColumn: false,
			inlineFilters: false,
			layout: "fixed",  // Use fixed layout to prevent CSS issues
			noDataMessage: __("No data found for the selected filters"),
			cellHeight: 600,  // Set a large height to accommodate the HTML content
			dynamicRowHeight: false,  // Disable dynamic height to prevent CSS issues
			serialNoColumn: false,
			headerHeight: 0,
			showTotalRow: false,
			resizeColumn: false,  // Disable column resizing
			sortable: false  // Disable sorting to prevent layout issues
		});
	}
});
