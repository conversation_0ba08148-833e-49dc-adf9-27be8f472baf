# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.utils import flt, getdate, formatdate
from frappe import _


def execute(filters=None):
	"""Main execution function for Contractor Ledger Side by Side report"""
	if not filters:
		filters = {}

	try:
		# Get data for both tables
		contractor_schedules = get_contractor_schedules(filters)
		contractor_payments = get_contractor_payments(filters)

		# Calculate summary metrics
		summary_data = calculate_summary_metrics(contractor_schedules, contractor_payments)

		# Create HTML report with side-by-side tables
		html_content = create_side_by_side_html_report(contractor_schedules, contractor_payments, summary_data)

		# Return single column with HTML content - increased width for horizontal scroll
		columns = [{"fieldname": "html_content", "label": "", "fieldtype": "HTML", "width": 1600}]
		data = [{"html_content": html_content}]

		return columns, data

	except Exception as e:
		frappe.log_error(f"Error in contractor ledger report: {str(e)}")
		return [], []


def get_contractor_schedules(filters):
	"""Fetch Contractor Schedule data from C Schedule Table with applied filters - only completed milestones"""
	conditions = ["cst.status = 'Completed'"]  # Only show completed milestones
	values = []

	if filters.get("project"):
		conditions.append("cs.project = %s")
		values.append(filters.get("project"))

	if filters.get("contractor"):
		conditions.append("cs.contractor = %s")
		values.append(filters.get("contractor"))

	if filters.get("from_date"):
		conditions.append("cst.c_date >= %s")
		values.append(filters.get("from_date"))

	if filters.get("to_date"):
		conditions.append("cst.c_date <= %s")
		values.append(filters.get("to_date"))

	where_clause = "WHERE " + " AND ".join(conditions)

	query = f"""
		SELECT
			cst.c_date,
			cst.milestone,
			cst.part,
			cst.amount,
			cst.retention_amount,
			cst.remarks,
			cs.contractor,
			cs.project
		FROM `tabC Schedule Table` cst
		INNER JOIN `tabC Schedule` cs ON cst.parent = cs.name
		{where_clause}
		ORDER BY cst.c_date DESC
	"""

	return frappe.db.sql(query, values, as_dict=True)


def get_contractor_payments(filters):
	"""Fetch Contractor Payments data with applied filters"""
	conditions = []
	values = []

	if filters.get("project"):
		conditions.append("project = %s")
		values.append(filters.get("project"))

	if filters.get("contractor"):
		conditions.append("contractor = %s")
		values.append(filters.get("contractor"))

	if filters.get("from_date"):
		conditions.append("date >= %s")
		values.append(filters.get("from_date"))

	if filters.get("to_date"):
		conditions.append("date <= %s")
		values.append(filters.get("to_date"))

	where_clause = ""
	if conditions:
		where_clause = "WHERE " + " AND ".join(conditions)

	query = f"""
		SELECT
			date,
			amount,
			mode_of_payment,
			remarks,
			reference_no,
			contractor,
			project
		FROM `tabContractor Payments`
		{where_clause}
		ORDER BY date DESC
	"""

	return frappe.db.sql(query, values, as_dict=True)


def calculate_summary_metrics(contractor_schedules, contractor_payments):
	"""Calculate summary metrics for the cards"""
	total_schedule_amount = sum(flt(schedule.get("amount", 0)) for schedule in contractor_schedules)
	total_retention_amount = sum(flt(schedule.get("retention_amount", 0)) for schedule in contractor_schedules)
	total_payments_made = sum(flt(payment.get("amount", 0)) for payment in contractor_payments)
	total_balance = total_schedule_amount - total_payments_made

	return {
		"total_schedule_amount": total_schedule_amount,
		"total_retention_amount": total_retention_amount,
		"total_payments_made": total_payments_made,
		"total_balance": total_balance
	}


def create_summary_cards_html(summary_data):
	"""Create summary cards HTML with light grey background and black text"""

	cards_html = f"""
	<div style='display: flex; justify-content: space-around; margin-bottom: 25px; flex-wrap: wrap; width: 100%; padding: 15px; gap: 15px;'>
		<!-- Total Schedule Amount Card -->
		<div style='background: #f8f9fa; padding: 22px; border-radius: 12px; text-align: center; min-width: 240px; flex: 1; max-width: 280px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); border: 1px solid #e9ecef;'>
			<div style='font-weight: 600; color: #333; margin-bottom: 12px; font-size: 13px; text-transform: uppercase; letter-spacing: 1px;'>🏗️ Total Schedule Amount</div>
			<div style='font-size: 24px; color: #000; font-weight: 700;'>{frappe.format(summary_data.get('total_schedule_amount', 0), {'fieldtype': 'Currency'})}</div>
		</div>

		<!-- Total Retention Amount Card -->
		<div style='background: #f8f9fa; padding: 22px; border-radius: 12px; text-align: center; min-width: 240px; flex: 1; max-width: 280px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); border: 1px solid #e9ecef;'>
			<div style='font-weight: 600; color: #333; margin-bottom: 12px; font-size: 13px; text-transform: uppercase; letter-spacing: 1px;'>🔒 Total Retention Amount</div>
			<div style='font-size: 24px; color: #000; font-weight: 700;'>{frappe.format(summary_data.get('total_retention_amount', 0), {'fieldtype': 'Currency'})}</div>
		</div>

		<!-- Total Payments Made Card -->
		<div style='background: #f8f9fa; padding: 22px; border-radius: 12px; text-align: center; min-width: 240px; flex: 1; max-width: 280px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); border: 1px solid #e9ecef;'>
			<div style='font-weight: 600; color: #333; margin-bottom: 12px; font-size: 13px; text-transform: uppercase; letter-spacing: 1px;'>💰 Total Payments Made</div>
			<div style='font-size: 24px; color: #000; font-weight: 700;'>{frappe.format(summary_data.get('total_payments_made', 0), {'fieldtype': 'Currency'})}</div>
		</div>

		<!-- Total Balance Card -->
		<div style='background: #f8f9fa; padding: 22px; border-radius: 12px; text-align: center; min-width: 240px; flex: 1; max-width: 280px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); border: 1px solid #e9ecef;'>
			<div style='font-weight: 600; color: #333; margin-bottom: 12px; font-size: 13px; text-transform: uppercase; letter-spacing: 1px;'>⚖️ Total Balance</div>
			<div style='font-size: 24px; color: #000; font-weight: 700;'>{frappe.format(summary_data.get('total_balance', 0), {'fieldtype': 'Currency'})}</div>
		</div>
	</div>
	"""

	return cards_html


def create_side_by_side_html_report(contractor_schedules, contractor_payments, summary_data):
	"""Create a complete HTML report with summary cards and side-by-side tables"""

	# Create summary cards
	summary_cards = create_summary_cards_html(summary_data)

	# Create contractor schedules table
	schedules_table = create_schedules_table_html(contractor_schedules)

	# Create contractor payments table
	payments_table = create_payments_table_html(contractor_payments)

	# Add debug info if no data found
	debug_info = ""
	if not contractor_schedules and not contractor_payments:
		# Get total counts to help with debugging
		total_schedules = frappe.db.count("C Schedule Table")
		total_payments = frappe.db.count("Contractor Payments")

		debug_info = f"""
		<div style='background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin-bottom: 20px; color: #856404;'>
			<strong>📊 Debug Information:</strong><br>
			• Total C Schedule Table records in system: {total_schedules}<br>
			• Total Contractor Payments records in system: {total_payments}<br>
			• Applied filters may be too restrictive or no data exists for the selected criteria.
		</div>
		"""

	# Combine everything into a complete report with horizontal scrolling
	html_report = f"""
	<div style='width: 100%; overflow-x: auto; padding: 20px; font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; min-width: 1400px;'>
		<!-- Summary Cards -->
		{summary_cards}

		{debug_info}

		<!-- Side by Side Tables Container -->
		<div style='display: flex; gap: 20px; margin-top: 20px; min-width: 1200px;'>
			<!-- Contractor Schedules Table (Left) -->
			<div style='flex: 1; min-width: 600px;'>
				<h3 style='color: #d32f2f; margin-bottom: 15px; font-size: 18px; font-weight: 600; border-bottom: 2px solid #d32f2f; padding-bottom: 8px;'>
					🏗️ Contractor Schedules ({len(contractor_schedules)} records)
				</h3>
				{schedules_table}
			</div>

			<!-- Contractor Payments Table (Right) -->
			<div style='flex: 1; min-width: 600px;'>
				<h3 style='color: #388e3c; margin-bottom: 15px; font-size: 18px; font-weight: 600; border-bottom: 2px solid #388e3c; padding-bottom: 8px;'>
					💰 Contractor Payments ({len(contractor_payments)} records)
				</h3>
				{payments_table}
			</div>
		</div>
	</div>
	"""

	return html_report


def create_schedules_table_html(contractor_schedules):
	"""Create HTML table for contractor schedules"""
	if not contractor_schedules:
		return """
		<div style='text-align: center; padding: 40px; color: #666; background: #f8f9fa; border-radius: 8px; border: 1px solid #dee2e6;'>
			<div style='font-size: 16px; margin-bottom: 8px;'>🏗️</div>
			<div style='font-weight: 500; margin-bottom: 5px;'>No contractor schedules found</div>
			<div style='font-size: 12px; color: #999;'>Try adjusting your filters</div>
		</div>
		"""

	table_html = """
	<div style='max-height: 400px; overflow-y: auto; overflow-x: auto; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border: 1px solid #e0e6ed;'>
		<table style='width: 100%; border-collapse: collapse; background: white; font-size: 13px;'>
			<thead style='position: sticky; top: 0; z-index: 10;'>
				<tr style='background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);'>
					<th style='padding: 12px 8px; text-align: left; font-weight: 600; color: #d32f2f; border-bottom: 2px solid #d32f2f; font-size: 12px;'>C Date</th>
					<th style='padding: 12px 8px; text-align: left; font-weight: 600; color: #d32f2f; border-bottom: 2px solid #d32f2f; font-size: 12px;'>Milestones</th>
					<th style='padding: 12px 8px; text-align: right; font-weight: 600; color: #d32f2f; border-bottom: 2px solid #d32f2f; font-size: 12px;'>Part</th>
					<th style='padding: 12px 8px; text-align: right; font-weight: 600; color: #d32f2f; border-bottom: 2px solid #d32f2f; font-size: 12px;'>Amount</th>
					<th style='padding: 12px 8px; text-align: right; font-weight: 600; color: #d32f2f; border-bottom: 2px solid #d32f2f; font-size: 12px;'>Retention Amount</th>
					<th style='padding: 12px 8px; text-align: left; font-weight: 600; color: #d32f2f; border-bottom: 2px solid #d32f2f; font-size: 12px;'>Remark</th>
				</tr>
			</thead>
			<tbody>
	"""

	for i, schedule in enumerate(contractor_schedules):
		row_bg = "#fff5f5" if i % 2 == 0 else "white"

		# Truncate remarks if too long
		remarks = str(schedule.get("remarks", "") or "")[:50]
		if len(remarks) > 47:
			remarks = remarks[:47] + "..."

		table_html += f"""
		<tr style='background: {row_bg}; border-bottom: 1px solid #e0e6ed;'>
			<td style='padding: 10px 8px; border-right: 1px solid #e0e6ed;'>{formatdate(schedule.get("c_date")) if schedule.get("c_date") else ""}</td>
			<td style='padding: 10px 8px; border-right: 1px solid #e0e6ed;'>{schedule.get("milestone", "")}</td>
			<td style='padding: 10px 8px; text-align: right; font-weight: 500; border-right: 1px solid #e0e6ed;'>{flt(schedule.get("part", 0))}</td>
			<td style='padding: 10px 8px; text-align: right; font-weight: 500; border-right: 1px solid #e0e6ed;'>{frappe.format(flt(schedule.get("amount", 0)), {"fieldtype": "Currency"}) if schedule.get("amount") else ""}</td>
			<td style='padding: 10px 8px; text-align: right; font-weight: 500; border-right: 1px solid #e0e6ed;'>{frappe.format(flt(schedule.get("retention_amount", 0)), {"fieldtype": "Currency"}) if schedule.get("retention_amount") else ""}</td>
			<td style='padding: 10px 8px;'>{remarks}</td>
		</tr>
		"""

	table_html += """
			</tbody>
		</table>
	</div>
	"""

	return table_html


def create_payments_table_html(contractor_payments):
	"""Create HTML table for contractor payments"""
	if not contractor_payments:
		return """
		<div style='text-align: center; padding: 40px; color: #666; background: #f8f9fa; border-radius: 8px; border: 1px solid #dee2e6;'>
			<div style='font-size: 16px; margin-bottom: 8px;'>💰</div>
			<div style='font-weight: 500; margin-bottom: 5px;'>No contractor payments found</div>
			<div style='font-size: 12px; color: #999;'>Try adjusting your filters</div>
		</div>
		"""

	table_html = """
	<div style='max-height: 400px; overflow-y: auto; overflow-x: auto; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border: 1px solid #e0e6ed;'>
		<table style='width: 100%; border-collapse: collapse; background: white; font-size: 13px;'>
			<thead style='position: sticky; top: 0; z-index: 10;'>
				<tr style='background: linear-gradient(135deg, #e8f5e8 0%, #a5d6a7 100%);'>
					<th style='padding: 12px 8px; text-align: left; font-weight: 600; color: #388e3c; border-bottom: 2px solid #388e3c; font-size: 12px;'>Date</th>
					<th style='padding: 12px 8px; text-align: right; font-weight: 600; color: #388e3c; border-bottom: 2px solid #388e3c; font-size: 12px;'>Amount</th>
					<th style='padding: 12px 8px; text-align: left; font-weight: 600; color: #388e3c; border-bottom: 2px solid #388e3c; font-size: 12px;'>Mode of Payment</th>
					<th style='padding: 12px 8px; text-align: left; font-weight: 600; color: #388e3c; border-bottom: 2px solid #388e3c; font-size: 12px;'>Remarks</th>
				</tr>
			</thead>
			<tbody>
	"""

	for i, payment in enumerate(contractor_payments):
		row_bg = "#f0fff4" if i % 2 == 0 else "white"
		remarks = str(payment.get("remarks", "") or "")[:50]
		if len(remarks) > 47:
			remarks = remarks[:47] + "..."

		table_html += f"""
		<tr style='background: {row_bg}; border-bottom: 1px solid #e0e6ed;'>
			<td style='padding: 10px 8px; border-right: 1px solid #e0e6ed;'>{formatdate(payment.get("date")) if payment.get("date") else ""}</td>
			<td style='padding: 10px 8px; text-align: right; font-weight: 500; border-right: 1px solid #e0e6ed;'>{frappe.format(flt(payment.get("amount", 0)), {"fieldtype": "Currency"}) if payment.get("amount") else ""}</td>
			<td style='padding: 10px 8px; border-right: 1px solid #e0e6ed;'>{payment.get("mode_of_payment", "")}</td>
			<td style='padding: 10px 8px;'>{remarks}</td>
		</tr>
		"""

	table_html += """
			</tbody>
		</table>
	</div>
	"""

	return table_html
