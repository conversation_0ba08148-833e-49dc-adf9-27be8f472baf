{"actions": [], "allow_rename": 1, "autoname": "format:{contractor}-{project}-{##########}", "creation": "2025-07-28 22:47:00.918807", "doctype": "DocType", "engine": "InnoDB", "field_order": ["date", "contractor", "project", "column_break_hpej", "amount", "mode_of_payment", "reference_no", "remarks"], "fields": [{"fieldname": "date", "fieldtype": "Date", "label": "Date"}, {"fieldname": "contractor", "fieldtype": "Link", "label": "Contractor", "options": "Contractors"}, {"fieldname": "project", "fieldtype": "Link", "label": "Project", "options": "Projects"}, {"fieldname": "column_break_hpej", "fieldtype": "Column Break"}, {"fieldname": "amount", "fieldtype": "Float", "label": "Amount"}, {"default": "Bank Transfer", "fieldname": "mode_of_payment", "fieldtype": "Select", "label": "Mode of payment", "options": "\nBank Transfer\nUPI\nCash\nCheque"}, {"fieldname": "reference_no", "fieldtype": "Data", "label": "Reference No"}, {"fieldname": "remarks", "fieldtype": "Small Text", "label": "Remarks"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-28 22:47:00.918807", "modified_by": "Administrator", "module": "Contractors", "name": "Contractor Payments", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}