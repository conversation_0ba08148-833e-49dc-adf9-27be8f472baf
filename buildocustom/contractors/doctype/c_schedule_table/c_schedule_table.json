{"actions": [], "allow_rename": 1, "creation": "2025-07-28 22:24:08.250419", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["milestone", "part", "amount", "retention_amount", "status", "c_date", "pymt_status", "remarks"], "fields": [{"fieldname": "milestone", "fieldtype": "Data", "in_list_view": 1, "label": "Milestone"}, {"fieldname": "part", "fieldtype": "Float", "in_list_view": 1, "label": "Part"}, {"fieldname": "amount", "fieldtype": "Float", "in_list_view": 1, "label": "Amount", "read_only": 1}, {"fieldname": "retention_amount", "fieldtype": "Float", "in_list_view": 1, "label": "Retention Amount", "read_only": 1}, {"default": "Not Started", "fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "label": "Status", "options": "Not Started\nCompleted"}, {"fieldname": "c_date", "fieldtype": "Date", "in_list_view": 1, "label": "C Date"}, {"fieldname": "pymt_status", "fieldtype": "Select", "in_list_view": 1, "label": "Pymt Status", "options": "Not Paid\nPartially Paid\nFully Paid"}, {"fieldname": "remarks", "fieldtype": "Data", "in_list_view": 1, "label": "Remarks"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2025-07-28 22:24:08.250419", "modified_by": "Administrator", "module": "Contractors", "name": "C Schedule Table", "owner": "Administrator", "permissions": [], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}