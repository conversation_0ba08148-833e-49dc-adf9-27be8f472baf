// Copyright (c) 2025, <PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.ui.form.on("P Schedules", {
    refresh(frm) {
        // Initial calculation on refresh if data exists
        frm.doc.table_xpyk.forEach(row => {
            calculate_amount(frm, row);
        });
        calculate_totals(frm);
    },

    total_value(frm) {
        frm.doc.table_xpyk.forEach(row => {
            calculate_amount(frm, row);
        });
        calculate_totals(frm);
    }
});

frappe.ui.form.on("P Schedules Table", {
    perc(frm, cdt, cdn) {
        const row = frappe.get_doc(cdt, cdn);
        calculate_amount(frm, row);
        calculate_totals(frm);
    },
    amount(frm, cdt, cdn) {
        // Recalculate totals if amount is manually changed (though it's calculated)
        calculate_totals(frm);
    },
    status(frm, cdt, cdn) {
        const row = frappe.get_doc(cdt, cdn);
        if (row.status === "Completed") {
            row.c_date = frappe.datetime.get_today();
        } else {
            row.c_date = null; // Clear date if status is not completed
        }
        frm.refresh_field("table_xpyk");

        // Progress will be updated automatically when the P Schedule is saved
        // No need for explicit client-side call
    }
});

function calculate_amount(frm, row) {
    const total_value = parseFloat(frm.doc.total_value) || 0;
    const perc = parseFloat(row.perc) || 0;
    row.amount = (perc * total_value) / 100;
    frm.refresh_field("table_xpyk");
}

function calculate_totals(frm) {
    let total_amount_sum = 0;
    let total_percentage_sum = 0;

    frm.doc.table_xpyk.forEach(row => {
        total_amount_sum += parseFloat(row.amount) || 0;
        total_percentage_sum += parseFloat(row.perc) || 0;
    });

    frm.set_value("total_amount", total_amount_sum);
    frm.set_value("total_percentage", total_percentage_sum);

    const total_value_parent = parseFloat(frm.doc.total_value) || 0;

    if (total_amount_sum > total_value_parent) {
        frappe.msgprint(__("Total Amount ({0}) cannot be greater than Total Value ({1})", [total_amount_sum, total_value_parent]));
    }

    if (total_percentage_sum > 100) {
        frappe.msgprint(__("Total Percentage ({0}) cannot be greater than 100", [total_percentage_sum]));
    }
}

