// Copyright (c) 2025, <PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.ui.form.on("BOQ", {
    refresh: function(frm) {
        frm.trigger('calculate_all_totals');
    },
    cgst_: function(frm) {
        frm.trigger('calculate_all_totals');
    },
    sgst_: function(frm) {
        frm.trigger('calculate_all_totals');
    },
    igst_: function(frm) {
        frm.trigger('calculate_all_totals');
    },
    calculate_all_totals: function(frm) {
        let sub_total = 0;
        frm.doc.table_cbvk.forEach(function(item) {
            sub_total += item.amount || 0;
        });
        frm.set_value('sub_total', sub_total);

        let cgst_amount = (frm.doc.cgst_ || 0) * sub_total / 100;
        let sgst_amount = (frm.doc.sgst_ || 0) * sub_total / 100;
        let igst_amount = (frm.doc.igst_ || 0) * sub_total / 100;

        frm.set_value('cgst_amount', cgst_amount);
        frm.set_value('sgst_amount', sgst_amount);
        frm.set_value('igst_amount', igst_amount);

        let grand_total = sub_total + cgst_amount + sgst_amount + igst_amount;
        frm.set_value('grand_total', grand_total);
    }
});

frappe.ui.form.on("BOQ Items Table", {
    qty: function(frm, cdt, cdn) {
        let row = frappe.get_doc(cdt, cdn);
        row.amount = (row.qty || 0) * (row.rate || 0);
        frm.refresh_field('table_cbvk');
        frm.trigger('calculate_all_totals');
    },
    rate: function(frm, cdt, cdn) {
        let row = frappe.get_doc(cdt, cdn);
        row.amount = (row.qty || 0) * (row.rate || 0);
        frm.refresh_field('table_cbvk');
        frm.trigger('calculate_all_totals');
    },
    amount: function(frm) {
        frm.trigger('calculate_all_totals');
    },
    status: function(frm, cdt, cdn) {
        // Progress will be updated automatically when the BOQ is saved
        // No need for explicit client-side call
    },
    table_cbvk_remove: function(frm) {
        frm.trigger('calculate_all_totals');
    }
});
