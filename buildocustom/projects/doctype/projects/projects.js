// Copyright (c) 2025, <PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.ui.form.on("Projects", {
    refresh: function(frm) {
        frm.trigger('calculate_total_value');
    },
    area: function(frm) {
        frm.trigger('calculate_total_value');
    },
    rate: function(frm) {
        frm.trigger('calculate_total_value');
    },
    calculate_total_value: function(frm) {
        if (frm.doc.area && frm.doc.rate) {
            frm.set_value('total_value', frm.doc.area * frm.doc.rate);
        } else {
            frm.set_value('total_value', 0);
        }
    }
});