{"actions": [], "allow_rename": 1, "autoname": "format:{project}-{for_month_year}-{department}-{#########}", "creation": "2025-06-19 15:34:49.685345", "doctype": "DocType", "engine": "InnoDB", "field_order": ["project", "column_break_uvqc", "for_month_year", "column_break_yrcl", "department", "section_break_digg", "table_xwtx", "section_break_jonn", "total_amount", "column_break_gsfx", "total_advance", "column_break_cikv", "total_net_payable", "section_break_ddys", "remarks"], "fields": [{"fieldname": "project", "fieldtype": "Link", "label": "Project", "options": "Projects"}, {"fieldname": "column_break_uvqc", "fieldtype": "Column Break"}, {"fieldname": "for_month_year", "fieldtype": "Data", "label": "For Month & Year"}, {"fieldname": "column_break_yrcl", "fieldtype": "Column Break"}, {"fieldname": "department", "fieldtype": "Select", "label": "Department", "options": "\nCarpenter\nFitter\nLabour\nOthers"}, {"fieldname": "section_break_digg", "fieldtype": "Section Break"}, {"fieldname": "table_xwtx", "fieldtype": "Table", "options": "Hajri Table"}, {"fieldname": "section_break_ddys", "fieldtype": "Section Break"}, {"fieldname": "remarks", "fieldtype": "Small Text", "label": "Remarks"}, {"fieldname": "section_break_jonn", "fieldtype": "Section Break"}, {"fieldname": "total_amount", "fieldtype": "Float", "label": "Total Amount"}, {"fieldname": "column_break_gsfx", "fieldtype": "Column Break"}, {"fieldname": "total_advance", "fieldtype": "Float", "label": "Total Advance"}, {"fieldname": "column_break_cikv", "fieldtype": "Column Break"}, {"fieldname": "total_net_payable", "fieldtype": "Float", "label": "Total Net Payable"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-06-21 18:24:45.967674", "modified_by": "Administrator", "module": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}