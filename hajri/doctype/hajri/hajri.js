// Copyright (c) 2025, <PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.ui.form.on("Hajri", {
    refresh(frm) {
        frm.trigger("calculate_totals");
    },

    calculate_totals(frm) {
        let total_amount = 0;
        let total_advance = 0;
        let total_net_payable = 0;

        frm.doc.table_xwtx.forEach(function(row) {
            total_amount += (row.amount || 0);
            total_advance += (row.advances || 0);
            total_net_payable += (row.net_payable || 0);
        });

        frm.set_value("total_amount", total_amount);
        frm.set_value("total_advance", total_advance);
        frm.set_value("total_net_payable", total_net_payable);
    }
});

frappe.ui.form.on("Hajri Table", {
    rate: function(frm, cdt, cdn) {
        let row = locals[cdt][cdn];
        row.amount = (row.rate || 0) * (row.total_hajri || 0);
        row.net_payable = (row.amount || 0) - (row.advances || 0);
        frm.refresh_field("table_xwtx");
        frm.trigger("calculate_totals");
    },
    total_hajri: function(frm, cdt, cdn) {
        let row = locals[cdt][cdn];
        row.amount = (row.rate || 0) * (row.total_hajri || 0);
        row.net_payable = (row.amount || 0) - (row.advances || 0);
        frm.refresh_field("table_xwtx");
        frm.trigger("calculate_totals");
    },
    advances: function(frm, cdt, cdn) {
        let row = locals[cdt][cdn];
        row.net_payable = (row.amount || 0) - (row.advances || 0);
        frm.refresh_field("table_xwtx");
        frm.trigger("calculate_totals");
    },
    net_payable: function(frm, cdt, cdn) {
        frm.trigger("calculate_totals");
    },
    table_xwtx_remove: function(frm, cdt, cdn) {
        frm.trigger("calculate_totals");
    }
});
