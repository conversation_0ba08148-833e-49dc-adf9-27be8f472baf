# Copyright (c) 2025, <PERSON><PERSON> and Contributors
# See license.txt

import frappe
from frappe.tests.utils import FrappeTestCase


class TestProjects(FrappeTestCase):
	def test_item_rate_total_value_calculation(self):
		"""Test that total value is calculated correctly for Item Rate contract type"""
		# Create a test project with Item Rate contract type
		project = frappe.get_doc({
			"doctype": "Projects",
			"project_name": "Test Item Rate Project",
			"contract_type": "Item Rate",
			"customer": "Test Customer"
		})
		project.insert()

		# Create test BOQs for the project
		boq1 = frappe.get_doc({
			"doctype": "BOQ",
			"project": project.name,
			"boq_no": "BOQ-001",
			"grand_total": 50000
		})
		boq1.insert()

		boq2 = frappe.get_doc({
			"doctype": "BOQ",
			"project": project.name,
			"boq_no": "BOQ-002",
			"grand_total": 75000
		})
		boq2.insert()

		# Reload project and check total value
		project.reload()
		project.calculate_total_value()

		# Total value should be sum of BOQ grand totals (50000 + 75000 = 125000)
		self.assertEqual(project.total_value, 125000)

		# Clean up
		boq1.delete()
		boq2.delete()
		project.delete()

	def test_built_up_total_value_calculation(self):
		"""Test that total value is calculated correctly for Built Up contract type"""
		# Create a test project with Built Up contract type
		project = frappe.get_doc({
			"doctype": "Projects",
			"project_name": "Test Built Up Project",
			"contract_type": "Built Up",
			"customer": "Test Customer",
			"area": 1000,
			"rate": 500
		})
		project.insert()

		# Check total value calculation
		project.calculate_total_value()

		# Total value should be area * rate (1000 * 500 = 500000)
		self.assertEqual(project.total_value, 500000)

		# Clean up
		project.delete()
