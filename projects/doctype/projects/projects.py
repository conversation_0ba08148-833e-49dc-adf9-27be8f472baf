# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
from frappe.utils import flt


class Projects(Document):
	def validate(self):
		"""Calculate progress percentage and total value based on contract type"""
		self.calculate_total_value()
		self.calculate_progress()

	def calculate_total_value(self):
		"""Calculate total value based on contract type"""
		if not self.contract_type:
			return

		if self.contract_type == "Item Rate":
			self.total_value = self._calculate_item_rate_total_value()
		elif self.contract_type == "Built Up":
			# For Built Up, total value is calculated as area * rate
			if self.area and self.rate:
				self.total_value = flt(self.area) * flt(self.rate)
			else:
				self.total_value = 0

	def calculate_progress(self):
		"""Calculate progress percentage based on contract type"""
		if not self.contract_type:
			self.progress_ = 0
			return

		if self.contract_type == "Built Up":
			self.progress_ = self._calculate_built_up_progress()
		elif self.contract_type == "Item Rate":
			self.progress_ = self._calculate_item_rate_progress()
		else:
			self.progress_ = 0

	def _calculate_built_up_progress(self):
		"""Calculate progress for Built Up contract type based on P Schedule milestones"""
		# Get the P Schedule for this project
		p_schedule = frappe.get_all(
			"P Schedules",
			filters={"project": self.name},
			fields=["name"],
			limit=1
		)

		if not p_schedule:
			return 0

		# Get milestone data from P Schedules Table
		milestones = frappe.get_all(
			"P Schedules Table",
			filters={"parent": p_schedule[0].name},
			fields=["status", "perc"]
		)

		if not milestones:
			return 0

		# Calculate progress based on completed milestones
		total_percentage = 0
		completed_percentage = 0

		for milestone in milestones:
			milestone_perc = flt(milestone.get("perc", 0))
			total_percentage += milestone_perc

			if milestone.get("status") == "Completed":
				completed_percentage += milestone_perc

		# Calculate progress percentage
		if total_percentage > 0:
			progress = (completed_percentage / total_percentage) * 100
			return round(progress, 2)

		return 0

	def _calculate_item_rate_total_value(self):
		"""Calculate total value for Item Rate contract type by summing BOQ grand totals"""
		# Get all BOQs for this project
		boqs = frappe.get_all(
			"BOQ",
			filters={"project": self.name},
			fields=["grand_total"]
		)

		if not boqs:
			return 0

		# Sum all grand totals
		total_value = 0
		for boq in boqs:
			total_value += flt(boq.get("grand_total", 0))

		return total_value

	def _calculate_item_rate_progress(self):
		"""Calculate progress for Item Rate contract type based on BOQ items"""
		# Get all BOQs for this project
		boqs = frappe.get_all(
			"BOQ",
			filters={"project": self.name},
			fields=["name"]
		)

		if not boqs:
			return 0

		total_items = 0
		completed_items = 0

		# Loop through all BOQs and their items
		for boq in boqs:
			boq_items = frappe.get_all(
				"BOQ Items Table",
				filters={"parent": boq.name},
				fields=["status"]
			)

			for item in boq_items:
				total_items += 1
				if item.get("status") == "Completed":
					completed_items += 1

		# Calculate progress percentage
		if total_items > 0:
			progress = (completed_items / total_items) * 100
			return round(progress, 2)

		return 0


@frappe.whitelist()
def calculate_project_progress(project_name, contract_type):
	"""Server method to calculate project progress"""
	try:
		project_doc = frappe.get_doc("Projects", project_name)

		if contract_type == "Built Up":
			progress = project_doc._calculate_built_up_progress()
		elif contract_type == "Item Rate":
			progress = project_doc._calculate_item_rate_progress()
		else:
			progress = 0

		return progress
	except Exception as e:
		frappe.log_error(f"Error calculating project progress: {str(e)}")
		return 0


@frappe.whitelist()
def update_project_total_value(project_name):
	"""Server method to update project total value when BOQs are modified"""
	try:
		project_doc = frappe.get_doc("Projects", project_name)

		if project_doc.contract_type == "Item Rate":
			# Calculate new total value from BOQ grand totals
			new_total_value = project_doc._calculate_item_rate_total_value()

			# Update the project's total value
			project_doc.total_value = new_total_value
			project_doc.save(ignore_permissions=True)

			return new_total_value

		return project_doc.total_value
	except Exception as e:
		frappe.log_error(f"Error updating project total value: {str(e)}")
		return 0
