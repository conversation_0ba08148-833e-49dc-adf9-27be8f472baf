# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document


class BOQ(Document):
	def on_update(self):
		"""Update project progress when BOQ is updated"""
		if self.project:
			self.update_project_progress()

	def on_trash(self):
		"""Update project progress when BOQ is deleted"""
		if self.project:
			self.update_project_progress()

	def update_project_progress(self):
		"""Update the progress percentage for the linked project"""
		try:
			project_doc = frappe.get_doc("Projects", self.project)
			project_doc.calculate_progress()
			project_doc.save(ignore_permissions=True)
		except Exception as e:
			frappe.log_error(f"Error updating project progress from BOQ {self.name}: {str(e)}")
