{"actions": [], "allow_rename": 1, "autoname": "field:boq_no", "creation": "2025-06-18 18:27:51.663511", "doctype": "DocType", "engine": "InnoDB", "field_order": ["project", "boq_for", "status", "column_break_rkyv", "date", "boq_no", "section_break_hmnj", "table_cbvk", "section_break_rwob", "cgst_", "sgst_", "igst_", "column_break_jyxg", "sub_total", "cgst_amount", "sgst_amount", "igst_amount", "grand_total", "section_break_guga", "remarks"], "fields": [{"fieldname": "project", "fieldtype": "Link", "label": "Project", "options": "Projects"}, {"fieldname": "date", "fieldtype": "Date", "label": "Date"}, {"fieldname": "boq_no", "fieldtype": "Data", "label": "BOQ No", "unique": 1}, {"fieldname": "boq_for", "fieldtype": "Data", "label": "BOQ For"}, {"default": "Not Started", "fieldname": "status", "fieldtype": "Select", "label": "Status", "options": "\nNot Started\nStarted"}, {"fieldname": "section_break_hmnj", "fieldtype": "Section Break"}, {"fieldname": "table_cbvk", "fieldtype": "Table", "options": "BOQ Items Table"}, {"fieldname": "column_break_rkyv", "fieldtype": "Column Break"}, {"fieldname": "section_break_rwob", "fieldtype": "Section Break"}, {"fieldname": "cgst_", "fieldtype": "Float", "label": "CGST %"}, {"fieldname": "sgst_", "fieldtype": "Float", "label": "SGST %"}, {"fieldname": "igst_", "fieldtype": "Float", "label": "IGST %"}, {"fieldname": "column_break_jyxg", "fieldtype": "Column Break"}, {"fieldname": "sub_total", "fieldtype": "Float", "label": "Sub Total"}, {"fieldname": "cgst_amount", "fieldtype": "Float", "label": "CGST Amount"}, {"fieldname": "sgst_amount", "fieldtype": "Float", "label": "SGST Amount"}, {"fieldname": "igst_amount", "fieldtype": "Float", "label": "IGST Amount"}, {"fieldname": "grand_total", "fieldtype": "Float", "label": "Grand Total"}, {"fieldname": "section_break_guga", "fieldtype": "Section Break"}, {"fieldname": "remarks", "fieldtype": "Small Text", "label": "Remarks"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-06-18 18:39:27.094696", "modified_by": "Administrator", "module": "Projects", "name": "BOQ", "naming_rule": "Expression (old style)", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}