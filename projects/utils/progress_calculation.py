# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.utils import flt


def update_project_progress_on_p_schedule_save(doc, method):
	"""Update project progress when P Schedule is saved"""
	if doc.project:
		update_project_progress(doc.project)


def update_project_progress_on_p_schedule_delete(doc, method):
	"""Update project progress when P Schedule is deleted"""
	if doc.project:
		update_project_progress(doc.project)


def update_project_progress_on_boq_save(doc, method):
	"""Update project progress when BOQ is saved"""
	if doc.project:
		update_project_progress(doc.project)


def update_project_progress_on_boq_delete(doc, method):
	"""Update project progress when BOQ is deleted"""
	if doc.project:
		update_project_progress(doc.project)


@frappe.whitelist()
def update_project_progress(project_name):
	"""Update the progress percentage for a project"""
	try:
		project_doc = frappe.get_doc("Projects", project_name)
		project_doc.calculate_progress()
		project_doc.save(ignore_permissions=True)
		frappe.db.commit()
		return {"status": "success", "message": f"Progress updated for project {project_name}"}
	except Exception as e:
		frappe.log_error(f"Error updating project progress for {project_name}: {str(e)}")
		return {"status": "error", "message": str(e)}


@frappe.whitelist()
def recalculate_all_project_progress():
	"""Utility function to recalculate progress for all projects"""
	projects = frappe.get_all("Projects", fields=["name"])
	
	for project in projects:
		try:
			update_project_progress(project.name)
		except Exception as e:
			frappe.log_error(f"Error recalculating progress for project {project.name}: {str(e)}")
	
	return f"Progress recalculated for {len(projects)} projects"
