# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe


@frappe.whitelist()
def test_progress_calculation():
	"""Test function to verify progress calculation is working"""
	results = []
	
	# Get all projects
	projects = frappe.get_all("Projects", fields=["name", "contract_type", "progress_"])
	
	for project in projects:
		try:
			project_doc = frappe.get_doc("Projects", project.name)
			old_progress = project.progress_ or 0
			
			# Recalculate progress
			project_doc.calculate_progress()
			new_progress = project_doc.progress_
			
			result = {
				"project": project.name,
				"contract_type": project.contract_type,
				"old_progress": old_progress,
				"new_progress": new_progress,
				"changed": old_progress != new_progress
			}
			
			# Add details based on contract type
			if project.contract_type == "Built Up":
				# Get P Schedule details
				p_schedules = frappe.get_all(
					"P Schedules",
					filters={"project": project.name},
					fields=["name"]
				)
				
				if p_schedules:
					milestones = frappe.get_all(
						"P Schedules Table",
						filters={"parent": p_schedules[0].name},
						fields=["milestone", "status", "perc"]
					)
					
					total_milestones = len(milestones)
					completed_milestones = len([m for m in milestones if m.status == "Completed"])
					
					result["details"] = {
						"total_milestones": total_milestones,
						"completed_milestones": completed_milestones,
						"milestones": milestones
					}
				else:
					result["details"] = {"error": "No P Schedule found"}
			
			elif project.contract_type == "Item Rate":
				# Get BOQ details
				boqs = frappe.get_all(
					"BOQ",
					filters={"project": project.name},
					fields=["name"]
				)
				
				total_items = 0
				completed_items = 0
				
				for boq in boqs:
					items = frappe.get_all(
						"BOQ Items Table",
						filters={"parent": boq.name},
						fields=["description", "status"]
					)
					
					total_items += len(items)
					completed_items += len([i for i in items if i.status == "Completed"])
				
				result["details"] = {
					"total_boqs": len(boqs),
					"total_items": total_items,
					"completed_items": completed_items
				}
			
			results.append(result)
			
		except Exception as e:
			results.append({
				"project": project.name,
				"error": str(e)
			})
	
	return results


@frappe.whitelist()
def get_project_progress_details(project_name):
	"""Get detailed progress information for a specific project"""
	try:
		project_doc = frappe.get_doc("Projects", project_name)
		
		result = {
			"project": project_name,
			"contract_type": project_doc.contract_type,
			"current_progress": project_doc.progress_
		}
		
		if project_doc.contract_type == "Built Up":
			result["built_up_details"] = project_doc._calculate_built_up_progress()
		elif project_doc.contract_type == "Item Rate":
			result["item_rate_details"] = project_doc._calculate_item_rate_progress()
		
		return result
		
	except Exception as e:
		return {"error": str(e)}
