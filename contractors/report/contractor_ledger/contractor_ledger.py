# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe import _
from frappe.utils import flt, getdate, formatdate


def execute(filters=None):
	"""
	Main execution function for Contractor Ledger report

	Args:
		filters (dict): Report filters containing project, contractor, from_date, to_date

	Returns:
		tuple: (columns, data, message, chart, report_summary) for the report
	"""
	if not filters:
		filters = {}

	# No validation required - show all data if no filters selected

	# Get columns definition
	columns = get_columns(filters)

	# Get report data
	data = get_ledger_data(filters)

	# Calculate summary data for cards
	summary_data = calculate_summary_data(data)

	# Create report summary cards
	report_summary = get_report_summary(summary_data)

	return columns, data, None, None, report_summary


def get_columns(filters=None):
	"""
	Define columns for the Contractor Ledger report

	Args:
		filters (dict): Report filters to determine which columns to show

	Returns:
		list: Column definitions with formatting
	"""
	columns = [
		{
			"label": _("Date"),
			"fieldname": "date",
			"fieldtype": "Date",
			"width": 100
		},
		{
			"label": _("Type"),
			"fieldname": "type",
			"fieldtype": "Data",
			"width": 120
		}
	]

	# Add Contractor column if no specific contractor is selected
	if not filters or not filters.get("contractor"):
		columns.append({
			"label": _("Contractor"),
			"fieldname": "contractor",
			"fieldtype": "Link",
			"options": "Contractors",
			"width": 150
		})

	# Add Project column if no specific project is selected
	if not filters or not filters.get("project"):
		columns.append({
			"label": _("Project"),
			"fieldname": "project",
			"fieldtype": "Link",
			"options": "Projects",
			"width": 150
		})

	# Add remaining columns with custom colors
	columns.extend([
		{
			"label": _("Reference"),
			"fieldname": "reference",
			"fieldtype": "Data",
			"width": 170
		},
		{
			"label": _("Debit"),
			"fieldname": "debit",
			"fieldtype": "Currency",
			"width": 120
		},
		{
			"label": _("Credit"),
			"fieldname": "credit",
			"fieldtype": "Currency",
			"width": 120
		},
		{
			"label": _("Balance"),
			"fieldname": "balance",
			"fieldtype": "Currency",
			"width": 120
		}
	])

	return columns


def get_ledger_data(filters):
	"""
	Get ledger data combining schedules and payments with running balance

	Args:
		filters (dict): Report filters

	Returns:
		list: Ledger entries with running balance
	"""
	# Get schedule transactions (debits)
	schedule_transactions = get_schedule_transactions(filters)

	# Get payment transactions (credits)
	payment_transactions = get_payment_transactions(filters)

	# Combine and sort all transactions
	all_transactions = schedule_transactions + payment_transactions

	# If specific contractor is selected, calculate running balance normally
	if filters and filters.get("contractor"):
		all_transactions.sort(key=lambda x: (getdate(x['date']), x['type']))

		running_balance = 0
		ledger_data = []

		for transaction in all_transactions:
			debit = flt(transaction.get('debit', 0))
			credit = flt(transaction.get('credit', 0))

			# Update running balance
			running_balance += debit - credit

			# Add balance to transaction
			transaction['balance'] = running_balance

			ledger_data.append(transaction)

		return ledger_data

	# If no specific contractor, group by contractor and calculate running balance per contractor
	else:
		# Sort by contractor, then by date
		all_transactions.sort(key=lambda x: (x.get('contractor', ''), getdate(x['date']), x['type']))

		contractor_balances = {}
		ledger_data = []

		for transaction in all_transactions:
			contractor = transaction.get('contractor', '')
			debit = flt(transaction.get('debit', 0))
			credit = flt(transaction.get('credit', 0))

			# Initialize contractor balance if not exists
			if contractor not in contractor_balances:
				contractor_balances[contractor] = 0

			# Update contractor running balance
			contractor_balances[contractor] += debit - credit

			# Add balance to transaction
			transaction['balance'] = contractor_balances[contractor]

			ledger_data.append(transaction)

		return ledger_data


def get_schedule_transactions(filters):
	"""
	Get schedule transactions (debit entries) for the contractor ledger
	Only includes completed milestones from C Schedule Table

	Args:
		filters (dict): Report filters

	Returns:
		list: Schedule transactions with calculated debit amounts
	"""
	conditions = []
	values = []

	# Contractor filter (optional)
	if filters.get("contractor"):
		conditions.append("cs.contractor = %s")
		values.append(filters.get("contractor"))

	# Project filter (optional)
	if filters.get("project"):
		conditions.append("cs.project = %s")
		values.append(filters.get("project"))

	# Date range filters - using c_date from table
	if filters.get("from_date"):
		conditions.append("cst.c_date >= %s")
		values.append(filters.get("from_date"))

	if filters.get("to_date"):
		conditions.append("cst.c_date <= %s")
		values.append(filters.get("to_date"))

	# Only completed milestones with c_date
	conditions.append("cst.status = 'Completed'")
	conditions.append("cst.c_date IS NOT NULL")

	# Only non-cancelled schedules
	conditions.append("cs.docstatus != 2")

	where_clause = " AND ".join(conditions) if conditions else "1=1"

	query = f"""
		SELECT
			cst.c_date as date,
			cs.name as schedule_name,
			cs.schedule_id,
			cs.schedule_for,
			cst.milestone,
			cst.amount,
			cs.contractor,
			cs.project
		FROM `tabC Schedule` cs
		INNER JOIN `tabC Schedule Table` cst ON cs.name = cst.parent
		WHERE {where_clause}
		ORDER BY cst.c_date ASC, cs.creation ASC
	"""

	schedules = frappe.db.sql(query, values, as_dict=True)

	transactions = []
	for schedule in schedules:
		amount = flt(schedule.amount)

		# Only include if amount is positive
		if amount > 0:
			# Create descriptive reference
			reference = f"{schedule.schedule_id or schedule.schedule_name}"
			if schedule.milestone:
				reference += f" - {schedule.milestone}"
			if schedule.schedule_for:
				reference += f" ({schedule.schedule_for})"

			transactions.append({
				'date': schedule.date,
				'type': 'Schedule',
				'contractor': schedule.contractor,
				'project': schedule.project,
				'reference': reference,
				'debit': amount,
				'credit': 0,
				'schedule_name': schedule.schedule_name
			})

	return transactions


def get_payment_transactions(filters):
	"""
	Get payment transactions (credit entries) for the contractor ledger

	Args:
		filters (dict): Report filters

	Returns:
		list: Payment transactions
	"""
	conditions = []
	values = []

	# Contractor filter (optional)
	if filters.get("contractor"):
		conditions.append("contractor = %s")
		values.append(filters.get("contractor"))

	# Project filter (optional)
	if filters.get("project"):
		conditions.append("project = %s")
		values.append(filters.get("project"))

	# Date range filters
	if filters.get("from_date"):
		conditions.append("date >= %s")
		values.append(filters.get("from_date"))

	if filters.get("to_date"):
		conditions.append("date <= %s")
		values.append(filters.get("to_date"))

	# Only non-cancelled payments
	conditions.append("docstatus != 2")

	where_clause = " AND ".join(conditions) if conditions else "1=1"

	query = f"""
		SELECT
			date,
			name,
			amount,
			contractor,
			project,
			mode_of_payment,
			reference_no
		FROM `tabContractor Payments`
		WHERE {where_clause}
		ORDER BY date ASC, creation ASC
	"""

	payments = frappe.db.sql(query, values, as_dict=True)

	transactions = []
	for payment in payments:
		amount = flt(payment.amount)

		# Only include if amount is positive
		if amount > 0:
			reference = payment.name
			if payment.reference_no:
				reference += f" (Ref: {payment.reference_no})"

			# Add payment mode for better identification
			if payment.mode_of_payment:
				reference += f" - {payment.mode_of_payment}"

			transactions.append({
				'date': payment.date,
				'type': 'Payment',
				'contractor': payment.contractor,
				'project': payment.project,
				'reference': reference,
				'debit': 0,
				'credit': amount,
				'payment_name': payment.name
			})

	return transactions


def calculate_summary_data(data):
	"""
	Calculate summary data from ledger transactions

	Args:
		data (list): Ledger data with transactions

	Returns:
		dict: Summary data with totals
	"""
	total_schedule_amount = 0
	total_payment_paid = 0
	running_balance = 0

	if data:
		for transaction in data:
			debit = flt(transaction.get('debit', 0))
			credit = flt(transaction.get('credit', 0))

			total_schedule_amount += debit
			total_payment_paid += credit

			# The last transaction will have the final running balance
			running_balance = flt(transaction.get('balance', 0))

	return {
		'total_schedule_amount': total_schedule_amount,
		'total_payment_paid': total_payment_paid,
		'running_balance': running_balance
	}


def get_report_summary(summary_data):
	"""
	Create report summary cards for display at the top of the report

	Args:
		summary_data (dict): Summary data with totals

	Returns:
		list: Report summary cards
	"""
	currency = frappe.defaults.get_user_default("currency") or "INR"

	# Determine balance color based on positive/negative
	balance_color = "red" if summary_data['running_balance'] < 0 else "blue"
	balance_indicator = "red" if summary_data['running_balance'] < 0 else "blue"

	return [
		{
			"value": summary_data['total_schedule_amount'],
			"label": _("Total Schedule Amount"),
			"datatype": "Currency",
			"currency": currency,
			"indicator": "red"
		},
		{
			"value": summary_data['total_payment_paid'],
			"label": _("Total Payment Paid"),
			"datatype": "Currency",
			"currency": currency,
			"indicator": "green"
		},
		{
			"value": summary_data['running_balance'],
			"label": _("Running Balance"),
			"datatype": "Currency",
			"currency": currency,
			"indicator": balance_indicator
		}
	]
