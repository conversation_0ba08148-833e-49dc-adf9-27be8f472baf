// Copyright (c) 2025, <PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.query_reports["Contractor Ledger"] = {
	"filters": [
		{
			"fieldname": "project",
			"label": __("Project"),
			"fieldtype": "Link",
			"options": "Projects",
			"width": "100px"
		},
		{
			"fieldname": "contractor",
			"label": __("Contractor"),
			"fieldtype": "Link",
			"options": "Contractors",
			"width": "100px"
		},
		{
			"fieldname": "from_date",
			"label": __("From Date"),
			"fieldtype": "Date",
			"default": frappe.datetime.add_months(frappe.datetime.get_today(), -1),
			"width": "100px"
		},
		{
			"fieldname": "to_date",
			"label": __("To Date"),
			"fieldtype": "Date",
			"default": frappe.datetime.get_today(),
			"width": "100px"
		}
	],

	"onload": function(report) {
		// Add custom styling for colored columns
		this.add_custom_styles();

		// Add refresh button
		report.page.add_inner_button(__("Refresh"), function() {
			report.refresh();
		});
	},

	"add_custom_styles": function() {
		// Add custom CSS for column colors
		if (!$('#contractor-ledger-custom-styles').length) {
			$('<style id="contractor-ledger-custom-styles">')
				.html(`
					/* Table headers - black text */
					.dt-header .dt-cell__content,
					.dt-header .dt-cell .dt-cell__content {
						color: #000000 !important;
						font-weight: 600;
					}

					/* Reference column - black text */
					.dt-cell[data-fieldname="reference"] .dt-cell__content {
						color: #000000 !important;
						font-weight: normal;
					}

					/* Debit column - red text */
					.dt-cell--col-4 .dt-cell__content,
					.dt-cell[data-fieldname="debit"] .dt-cell__content {
						color: #d73527 !important;
						font-weight: 500;
					}

					/* Credit column - green text */
					.dt-cell--col-5 .dt-cell__content,
					.dt-cell[data-fieldname="credit"] .dt-cell__content {
						color: #28a745 !important;
						font-weight: 500;
					}

					/* Balance column - blue text */
					.dt-cell--col-6 .dt-cell__content,
					.dt-cell[data-fieldname="balance"] .dt-cell__content {
						color: #007bff !important;
						font-weight: 600;
					}

					/* Handle negative balance in red */
					.dt-cell[data-fieldname="balance"] .dt-cell__content:contains('-') {
						color: #d73527 !important;
					}

					/* Summary cards styling */
					.report-summary .summary-item[data-label*="Schedule"] .summary-value {
						color: #d73527 !important;
					}

					.report-summary .summary-item[data-label*="Payment"] .summary-value {
						color: #28a745 !important;
					}

					.report-summary .summary-item[data-label*="Balance"] .summary-value {
						color: #007bff !important;
					}
				`)
				.appendTo('head');
		}
	},

	"formatter": function(value, row, column, data, default_formatter) {
		// Apply custom formatting for specific columns
		if (column.fieldname === "reference") {
			return `<span style="color: #000000; font-weight: normal;">${default_formatter(value, row, column, data)}</span>`;
		}

		if (column.fieldname === "debit" && value > 0) {
			return `<span style="color: #d73527; font-weight: 500;">${default_formatter(value, row, column, data)}</span>`;
		}

		if (column.fieldname === "credit" && value > 0) {
			return `<span style="color: #28a745; font-weight: 500;">${default_formatter(value, row, column, data)}</span>`;
		}

		if (column.fieldname === "balance") {
			const color = value < 0 ? "#d73527" : "#007bff";
			return `<span style="color: ${color}; font-weight: 600;">${default_formatter(value, row, column, data)}</span>`;
		}

		return default_formatter(value, row, column, data);
	}
};
