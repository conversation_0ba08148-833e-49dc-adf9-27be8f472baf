# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
from frappe.utils import flt


class CSchedule(Document):
	def validate(self):
		"""Validate and calculate amounts"""
		self.calculate_main_amounts()
		self.calculate_table_amounts()
		self.calculate_totals()
		self.validate_parts_total()

	def calculate_main_amounts(self):
		"""Calculate Total Amount and Per Part Amount"""
		rate = flt(self.rate)
		area = flt(self.area)
		t_parts = flt(self.t_parts)

		# Total Amount = rate * area
		self.total_amount = rate * area

		# Per Part Amount = total amount / Total Parts
		self.per_part_amount = self.total_amount / t_parts if t_parts > 0 else 0

	def calculate_table_amounts(self):
		"""Calculate amounts for each row in the table"""
		per_part_amount = flt(self.per_part_amount)
		retention_percent = flt(self.retention)

		for row in self.table_ydsq:
			part = flt(row.part)

			# amount = part * per part amount
			row.amount = part * per_part_amount

			# retention amount = retention * amount / 100
			row.retention_amount = (retention_percent * row.amount) / 100

	def calculate_totals(self):
		"""Calculate totals for the table"""
		parts_total = 0
		amount_total = 0
		retention_amount_total = 0

		for row in self.table_ydsq:
			parts_total += flt(row.part)
			amount_total += flt(row.amount)
			retention_amount_total += flt(row.retention_amount)

		self.parts_total = parts_total
		self.amount_total = amount_total
		self.retention_amount_total = retention_amount_total

	def validate_parts_total(self):
		"""Validate that parts total doesn't exceed total parts"""
		parts_total = flt(self.parts_total)
		t_parts = flt(self.t_parts)

		if parts_total > t_parts:
			frappe.throw(
				frappe._("Parts Total ({0}) cannot be greater than Total Parts ({1})").format(
					parts_total, t_parts
				)
			)
