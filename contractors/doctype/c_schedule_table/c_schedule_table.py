# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
from frappe.utils import today


class CScheduleTable(Document):
	def validate(self):
		"""Auto-populate C Date when status is marked as Completed"""
		if self.status == "Completed" and not self.c_date:
			self.c_date = today()
		elif self.status != "Completed":
			self.c_date = None
