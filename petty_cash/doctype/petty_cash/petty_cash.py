# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document


class PettyCash(Document):
	def validate(self):
		self.calculate_totals()

	def calculate_totals(self):
		"""Calculate total petty cash received, total expense, and balance in hand"""
		total_received = 0
		total_expense = 0

		# Sum up amounts from the child table
		for row in self.get("table_zznz", []):
			if row.receive_amt:
				total_received += row.receive_amt
			if row.exp_amt:
				total_expense += row.exp_amt

		# Update the calculated fields
		self.total_petty_cash_received = total_received
		self.total_expense = total_expense

		# Calculate balance in hand: Opening Balance + Total Received - Total Expense
		opening_balance = self.opening_balance or 0
		self.balance_in_hand = opening_balance + total_received - total_expense
